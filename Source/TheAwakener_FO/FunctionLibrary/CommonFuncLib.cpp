// Fill out your copyright notice in the Description page of Project Settings.


#include "CommonFuncLib.h"

#include "Algo/Accumulate.h"
#include "Engine/GameEngine.h"

int UCommonFuncLib::AliasMethodRandom(TArray<float> WeightPool,bool ConstructRate)
{
	int result =0;
	if (WeightPool.Num()<1)
	{
		UE_LOG(LogTemp,Warning,TEXT("Random Array is Empty"));
		return  result;
	}
	if (ConstructRate)
	{
		float Sum = 0;
		Sum = Algo::Accumulate(WeightPool,0.f,TPlus<>());
		for(auto &Element:WeightPool)
		{
			Element = Element/Sum;
		}
	}
	//AliasMethod init
	TArray<float> Prob;
	TArray<float> Alias;
	TArray<float> Less;
	TArray<float> More;

	for (auto Weight:WeightPool)
	{
		Prob.Add(-1);
		Alias.Add(-1);
	}

	//大小堆
	for (int i =0;i<WeightPool.Num();i++)
	{
		WeightPool[i]*=WeightPool.Num();
		if (WeightPool[i]>=1.f)
		{
			More.Add(i);
		}
		else if (WeightPool[i]>0.f)
		{
			Less.Add(i);
		}
	}

	while (!Less.IsEmpty()&&!More.IsEmpty())
	{
		 int LessIndex = *Less.begin();
		 int MoreIndex = *More.begin();
		Less.RemoveAt(0);
		More.RemoveAt(0);
		
		Prob[LessIndex] = WeightPool[LessIndex];
		Alias[LessIndex] = MoreIndex;

		WeightPool[MoreIndex] -= 1.0 -WeightPool[LessIndex];
		 if (WeightPool[MoreIndex] <1.f)
		 {
			 Less.Push(MoreIndex);
		 }
		 else
		 {
			 More.Push(MoreIndex);
		 }
	}
	
	while (!Less.IsEmpty())
	{
		Prob[Less[0]] = 1;
		Less.RemoveAt(0);
	}

	while (!More.IsEmpty())
	{
		Prob[More[0]] = 1;
		More.RemoveAt(0);
	}
	
	//GetResult
	result = FMath::RandRange(0,Prob.Num()-1);
	auto Seed = rand()/double(RAND_MAX);
	if (Seed>Prob[result])
	{
		result = Alias[result];
	}

	return result;	
}

bool UCommonFuncLib::SetWindowMode(EWindowMode::Type type)
{
	UGameEngine* gameEngine = Cast<UGameEngine>(GEngine);
	if (gameEngine)
	{
		TSharedPtr<SWindow> windowPtr = gameEngine->GameViewportWindow.Pin();
		SWindow* window = windowPtr.Get();
		if (window)
		{
			switch(type)
			{
				case EWindowMode::Type::Fullscreen:
				{
					window->Maximize();
						break;
				}
				case EWindowMode::Type::WindowedFullscreen :
				{
						window->Maximize();
						break;
				}
				case EWindowMode::Type::Windowed :
					{
						window->Minimize();
						break;
					}
				case EWindowMode::Type::NumWindowModes :
					{
						window->Minimize();
						break;
					}
			}
			return true;
		}
	}
	return false;
}
