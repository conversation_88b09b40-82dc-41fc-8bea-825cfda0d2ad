// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Kismet/GameplayStatics.h"
#include "CommonFuncLib.generated.h"

/**
 * 一些通用函数和算法
 * by Mu<PERSON>ian
 */

UCLASS()
class THEAWAKENER_FO_API UCommonFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	template <typename T>
	 static bool ArrayContainsArray(TArray<T>Sources,TArray<T>Targets,bool bFullContain = false)
	{
		if (Targets.IsEmpty())
		{
			return  true;
		}
		bool Res = false;
		if (bFullContain)
		{
			Res = true;
			for (auto Target:Targets)
			{
				if (!Sources.Contains(Target))
				{
					Res = false;
					break; ;
				}
			}
		}
		else
		{
			for (auto Target:Targets)
			{
				if (Sources.Contains(Target))
				{
					Res = true;
					break; ;
				}
			}
		}
		return Res;
	}
	
	UFUNCTION(BlueprintCallable)
	 static int AliasMethodRandom(TArray<float>WeightPool,bool ConstructRate = false);

	UFUNCTION(BlueprintCallable)
		static bool SetWindowMode(EWindowMode::Type type);
};


