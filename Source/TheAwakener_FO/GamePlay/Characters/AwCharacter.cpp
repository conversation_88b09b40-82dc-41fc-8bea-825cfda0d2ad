#include "AwCharacter.h"

#include "BubbleCage.h"
#include "SubjectiveActorComponent.h"
#include "Camera/CameraShakeBase.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Creation/MobModel.h"
#include "HitBox/ActorCatcher.h"
#include "HitBox/AttackHitBox.h"
#include "HitBox/Squeeze.h"
#include "HitBox/InterruptComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Net/UnrealNetwork.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/ECSDamageLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Dialog/CloseUpShotCamera.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/RogueInterface.h"

AAwCharacter::AAwCharacter()
{
	PrimaryActorTick.bCanEverTick = true;

	bUseControllerRotationPitch = false;
	bUseControllerRotationRoll = false;
	bUseControllerRotationYaw = true;

	AutoPossessAI = EAutoPossessAI::PlacedInWorldOrSpawned;

	CmdComponent = this->CreateDefaultSubobject<UCmdComponent>(TEXT("CmdComponent"));
	MoveComponent = this->CreateDefaultSubobject<UAwMoveComponent>(TEXT("AwMoveComponent"));
	ActionComponent = this->CreateDefaultSubobject<UActionComponent>(TEXT("ActionComponent"));
	AIComponent = this->GetLocalRole() == ENetRole::ROLE_Authority
		              ? this->CreateDefaultSubobject<UAwAIComponent>(TEXT("AIComponent"))
		              : nullptr;
	AttackHitComponent = this->CreateDefaultSubobject<UAttackHitManager>(TEXT("AttackHitManager"));
	BreakSystemComponent = this->CreateDefaultSubobject<UAwBreakSystemComponent>(TEXT("BreakSystemComponent"));
	AwPhysicalAnimComponent = this->CreateDefaultSubobject<UAwPhysicalBodyAnim>(TEXT("AwPhysicalAnimComponent"));

	if (GetMesh())
	{
		GetMesh()->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	}
}

void AAwCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AAwCharacter, OwnerPlayerController);
	DOREPLIFETIME(AAwCharacter, PlayerClassId);
	DOREPLIFETIME(AAwCharacter, MobClassId);
	DOREPLIFETIME(AAwCharacter, bUnderPlayerControl);
	DOREPLIFETIME(AAwCharacter, Side);
	//DOREPLIFETIME(AAwCharacter, CharacterObj);
	DOREPLIFETIME(AAwCharacter, CharacterAttachment);
	DOREPLIFETIME(AAwCharacter, Flyable);
	DOREPLIFETIME(AAwCharacter, SecondWind);
	DOREPLIFETIME(AAwCharacter, ControlState);
	DOREPLIFETIME(AAwCharacter, BaseControlState);
	DOREPLIFETIME(AAwCharacter, AttachControlState);
	DOREPLIFETIME(AAwCharacter, ActionControlState);
}

void AAwCharacter::Destroyed()
{
	Super::Destroyed();
	HideAllAppearance();
	RemoveAllBuff();
}


void AAwCharacter::InitClientByCharacterInfo(FAwCharacterInfo CharacterInfo)
{
	if (this->GetLocalRole() != ENetRole::ROLE_Authority)
		SetupByCharacterInfo(CharacterInfo);
}

void AAwCharacter::PreloadAssets()
{
	if (PreloadedObjects.Num() > 0)return;
	for (FSoftObjectPath path : Actions)
	{
		UObject* preloadObject = path.ResolveObject();
		if (!preloadObject)
		{
			preloadObject = path.TryLoad();
			PreloadedObjects.Add(preloadObject);
		}
	}
}

void AAwCharacter::GatherSubMeshes()
{
	TArray<USceneComponent*> BParts;
	if (GetMesh())
		GetMesh()->GetChildrenComponents(false, BParts);
	for (USceneComponent* BPart : BParts)
	{
		USkeletalMeshComponent* ThisMesh = Cast<USkeletalMeshComponent>(BPart);
		if (ThisMesh)
		{
			FString MeshName = ThisMesh->GetName();
			this->BodySightParts.Add(MeshName, ThisMesh);
			ThisMesh->SetLeaderPoseComponent(this->GetMesh(), true);
			continue;
		}
		UStaticMeshComponent* StaticMesh = Cast<UStaticMeshComponent>(BPart);
		if (StaticMesh)
		{
			FString MeshName = StaticMesh->GetName();
			this->BodySightParts.Add(MeshName, StaticMesh);
		}
		UChildActorComponent* ActorComp = Cast<UChildActorComponent>(BPart);
		if (ActorComp)
		{
			FString MeshName = ActorComp->GetName();
			this->BodySightActorParts.Add(MeshName, ActorComp);
		}
	}
}

void AAwCharacter::GatherActorComponents()
{
	TArray<UActorComponent*> ResComponents;
	this->GetComponents<UActorComponent>(ResComponents, true);
	for (const auto ResComponent : ResComponents)
	{
		if (ResComponent->GetAssetUserData<UCharacterHitBoxData>())
		{
			UCharacterHitBoxData* HitBoxData = ResComponent->GetAssetUserData<UCharacterHitBoxData>();
			HitBoxData->Id = ResComponent->GetName();
			TArray<FChaPart*> Parts = GetChaPartsByType(HitBoxData->PartType);
			if (Parts.Num())
			{
				if (HitBoxData->DefaultDefenseInfo.CharacterHitBoxId.Num() <= 0)
				{
					HitBoxData->DefaultDefenseInfo.CharacterHitBoxId.Add(HitBoxData->Id);
				}
				USceneComponent* SceneComponent = Cast<USceneComponent>(ResComponent);
				if (SceneComponent)
					CharacterHitBoxes.Add(TTuple<USceneComponent*, UCharacterHitBoxData*>(SceneComponent, HitBoxData));
			}
		}

		if (ResComponent->GetClass()->IsChildOf(UCharacterHitBox::StaticClass()) && this->GetLocalRole() ==
			ENetRole::ROLE_Authority)
		{
			//老的受击框做个兼容
			UCharacterHitBox* ChaBox = Cast<UCharacterHitBox>(ResComponent);
			UCharacterHitBoxData* ChaHitData = UCharacterHitBoxData::FromCharacterHitBox(ChaBox);
			TArray<FChaPart*> Parts = GetChaPartsByType(ChaBox->PartType);
			if (Parts.Num())
			{
				if (ChaHitData->DefaultDefenseInfo.CharacterHitBoxId.Num() <= 0)
					ChaHitData->DefaultDefenseInfo.CharacterHitBoxId.Add(ChaHitData->Id);

				USceneComponent* SComp = Cast<USceneComponent>(ResComponent);
				SComp->AddAssetUserData(ChaHitData);
				CharacterHitBoxes.Add(TTuple<USceneComponent*, UCharacterHitBoxData*>(SComp, ChaHitData));
			}
		}
		else if (ResComponent->GetClass()->IsChildOf(UAttachPoint::StaticClass()))
		{
			//抓点
			UAttachPoint* APoint = Cast<UAttachPoint>(ResComponent);
			if (APoint->Catcher == true)
			{
				this->CatchAttachPoints.Add(APoint->Id, APoint);
			}
			//不能else，因为一个点可能同时是抓点和被抓点
			if (APoint->Seat == true)
			{
				this->SeatAttachPoints.Add(APoint->Id, APoint);
			}
		}
		else if (ResComponent->GetClass()->IsChildOf(UCloseUpShotCamera::StaticClass()))
		{
			UCloseUpShotCamera* Cam = Cast<UCloseUpShotCamera>(ResComponent);
			if (Cam)
			{
				if (DialogCameraPoints.Contains(Cam->Index))
				{
					UKismetSystemLibrary::PrintString(this,
					                                  FString("Same Index Dialog Camera Replaced at Index ").Append(
						                                  FString::FromInt(Cam->Index)),
					                                  true, true, FLinearColor::Red, 10);
					DialogCameraPoints[Cam->Index] = Cam;
				}
				else
				{
					DialogCameraPoints.Add(Cam->Index, Cam);
				}
			}
		}
		else if (ResComponent->GetClass()->IsChildOf(UEquipmentBindPoint::StaticClass()))
		{
			//装备绑点
			UEquipmentBindPoint* EqPoint = Cast<UEquipmentBindPoint>(ResComponent);
			this->EquipmentBindPoints.Add(EqPoint->GetName(), EqPoint);
		}
		else if (ResComponent->GetClass()->IsChildOf(UMeshShock::StaticClass()))
		{
			this->MeshShocker = Cast<UMeshShock>(ResComponent);
		}
		else if (ResComponent->GetClass()->IsChildOf(USceneComponent::StaticClass()))
		{
			if (ResComponent->GetName() == "Mouth") //发声点
				MouthPoint = Cast<USceneComponent>(ResComponent);
			else if (ResComponent->GetName() == "TextBubble") //头顶泡泡点
				TextBubblePoint = Cast<USceneComponent>(ResComponent);
		}

		if (ResComponent->GetClass()->IsChildOf(UFXPlayPoint::StaticClass()))
		{
			FXPoint.Add(Cast<UFXPlayPoint>(ResComponent));
		}

		if (ResComponent->GetClass()->IsChildOf(UInteractWidgetComponent::StaticClass()))
		{
			InteractWidget = Cast<UInteractWidgetComponent>(ResComponent);
		}
	}
}

UCloseUpShotCamera* AAwCharacter::GetDialogCameraByIndex(int Index)
{
	if (!DialogCameraPoints.Contains(Index)) return nullptr;
	return DialogCameraPoints[Index];
}

void AAwCharacter::PauseAI(bool DoPause) const
{
	if (!AIComponent) return;
	AIComponent->Paused = DoPause;
}

void AAwCharacter::ConnectHitBoxToPart()
{
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBox : CharacterHitBoxes)
	{
		UCharacterHitBoxData* Box = HitBox.Get<1>();
		if (!Box) continue;
		for (int i = 0; i < this->CharacterObj.Part.Num(); i++)
		{
			if (Box->PartType == this->CharacterObj.Part[i].PartType)
				Box->BelongsToPart = &this->CharacterObj.Part[i];
		}
	}
}

void AAwCharacter::SetNpcInfo(FNpcInfo Info)
{
	this->NpcInfo = Info;
}

void AAwCharacter::SetupByCharacterInfo(FAwCharacterInfo CharacterInfo)
{
	this->CharacterObj = CharacterInfo;

	this->PlayerClassId = CharacterInfo.ClassId;
	UKismetSystemLibrary::PrintString(this, FString("PlayerClass : ").Append(CharacterInfo.ClassId));
	this->CharacterObj.TypeId = CharacterObj.TypeId;
	FBattleClassModel BattleClass = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(PlayerClassId);
	this->Setup(BattleClass, this->CharacterObj.TypeId);
	this->Flyable = false;
	this->SetToArms(EArmState::Unarmed);
	this->SetLevelTo(CharacterInfo.CharacterLevel);
	this->Side = 0;
	this->SecondWind = 2.0f;

	for (FString CurTag : BattleClass.Tags)
	{
		if (!this->CharacterObj.Tag.Contains(CurTag))
			this->CharacterObj.Tag.Add(CurTag);
	}

	InitPlayerChaPart();

	//获取角色的攻击框和受击框，加入到列表里
	GatherActorComponents();
	ConnectHitBoxToPart();
	GatherSubMeshes();

	//武器装备实际上是CharacterObj的数据，所以已经穿好了，只要刷一下
	//武器
	//this->WearWeapon(CharacterInfo.WeaponSet);

	//穿装备
	// for (const FEquipment ThisEquipment : CharacterInfo.Equipments)
	// {
	// 	this->WearEquipment(ThisEquipment);
	// }
	RefreshEquipmentAppearance();

	this->FullyRestore();

	LastLocation = GetActorLocation();

	if (UGameplayFuncLib::GetAwGameInstance()->RoleInfo.LoginTimes <= 0)
	{
		GiveNewRoleItems(UGameplayFuncLib::GetAwDataManager()->GetNewRoleItemsByCharacterType("TypeA")); //TODO 给的东西写死了
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.LoginTimes += 1;
	}

	this->InteractWidgetVisible(false);

	for (const TTuple<FString, FActionSelection> MRecord : this->CharacterObj.MainActionChangeRecord)
	{
		this->ActionComponent->ChangeMainAction(MRecord.Value, MRecord.Key, false);
	}
	for (const TTuple<FString, FActionLink> LRecord : this->CharacterObj.LinkedActionChangeRecord)
	{
		this->ActionComponent->ChangeLinkedAction(LRecord.Value, LRecord.Key, false);
	}

	if (UAwGameInstance::Instance->RoleInfo.QuickSlotItemId.Num() > 0)
	{
		UAwGameInstance::Instance->RoleInfo.AutoSetFocusItemTo(UAwGameInstance::Instance->RoleInfo.QuickSlotItemId[0]);
		UAwGameInstance::Instance->RoleInfo.SetQuickSlotFocusItemToUsingItem();
	}

	UGameplayFuncLib::SaveGame();
}

void AAwCharacter::GiveNewRoleItems(FNewRoleItem Items)
{
	if (!UGameplayFuncLib::GetAwGameInstance()) return;
	for (const TTuple<FString, int> Cur : Items.Currency)
	{
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.Currency.Add(Cur.Key, Cur.Value);
	}
	UAwGameInstance::Instance->RoleInfo.QuickSlotItemId.Empty();
	for (const TTuple<FString, int> PItem : Items.PackItems)
	{
		for (int i = 0; i < PItem.Value; i++)
		{
			UAwGameInstance::Instance->RoleInfo.ItemObjs.Add(FItemObj(
				UGameplayFuncLib::GetDataManager()->GetItemById(PItem.Key)
			));
		}
		UAwGameInstance::Instance->RoleInfo.QuickSlotItemId.Add(PItem.Key);
	}
	if (UAwGameInstance::Instance->RoleInfo.QuickSlotItemId.Num() > 0)
	{
		UAwGameInstance::Instance->RoleInfo.AutoSetFocusItemTo(UAwGameInstance::Instance->RoleInfo.QuickSlotItemId[0]);
		UAwGameInstance::Instance->RoleInfo.SetQuickSlotFocusItemToUsingItem();
	}

	for (FString PEquipmentModelId : Items.PackEquipmentModelId)
	{
		FEquipment Equipment = UGameplayFuncLib::GetDataManager()->GetEquipmentById(PEquipmentModelId);
		// TODO: UniqueId 创建
		Equipment.UniqueId = UEnum::GetValueAsString(Equipment.PartType)
			.Append(FDateTime::Now().ToString()
			                        .Append("_")
			                        .Append(FString::FromInt(FMath::RandRange(0, 999999))));
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs.Add(Equipment);
	}
	for (FString PWeaponModelId : Items.PackWeaponModelId)
	{
		FWeaponObj Weapon = FWeaponObj(UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(PWeaponModelId));
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs.Add(Weapon);
	}

	for (FString WEquipmentModelId : Items.WearEquipmentModelId)
		this->WearEquipmentById(WEquipmentModelId);

	FWeaponModel MainHand;
	FWeaponModel OffHand;
	if (Items.WearWeaponModelId.Num() > 0)
	{
		MainHand = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(Items.WearWeaponModelId[0]);
	}
	if (Items.WearWeaponModelId.Num() > 1)
	{
		OffHand = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(Items.WearWeaponModelId[1]);
	}
	FEquippedWeaponSet ToWear = FEquippedWeaponSet(CharacterObj.TypeId, MainHand, OffHand);
	this->WearWeapon(ToWear);

	//成就
	for (const FAchievementModel AModel : UGameplayFuncLib::GetDataManager()->AchievementModels)
	{
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.Achievements.Add(FAchievementObj(AModel));
	}
}

void AAwCharacter::WearRogueEquipments(FString ClassId)
{
	if (UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager())
	{
		FRoleType RoleInfo = DataManager->GetRolePawnByClassId(ClassId);
		for (const FString EquipmentId : RoleInfo.Equipments)
			this->WearEquipmentById(EquipmentId);

		// FWeaponModel MainHand;
		// FWeaponModel OffHand;
		// if (!RoleInfo.MainHandId.IsEmpty())
		// 	MainHand = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(RoleInfo.MainHandId);
		// if (!RoleInfo.OffHandId.IsEmpty())
		// 	OffHand = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(RoleInfo.OffHandId);
		// FEquippedWeaponSet ToWear = FEquippedWeaponSet(CharacterObj.TypeId, MainHand, OffHand);
		// this->WearWeapon(ToWear);
	}
}

void AAwCharacter::WearRogueWeapon(FString RogueWeaponInfoId)
{
	if (UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager())
	{
		FRogueWeaponInfo RogueWeaponInfo = DataManager->GetRogueWeaponInfo(RogueWeaponInfoId);

		FWeaponModel MainHand;
		FWeaponModel OffHand;
		if (!RogueWeaponInfo.MainHand.IsEmpty())
			MainHand = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(RogueWeaponInfo.MainHand);
		if (!RogueWeaponInfo.OffHand.IsEmpty())
			OffHand = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(RogueWeaponInfo.OffHand);
		FEquippedWeaponSet ToWear = FEquippedWeaponSet(CharacterObj.TypeId, MainHand, OffHand);
		this->WearWeapon(ToWear);

		UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		FString WeaponId = RogueWeaponInfo.Id;
		const int Level = RogueDataSystem->GetWeaponLevel(WeaponId);
		URogueWeapon::AddWeaponBuffs(this, RogueWeaponInfo, Level);
	}
}

void AAwCharacter::SetupAsMonster(FMobModel MobModel, int InitLevel, int ToSide, EMobRank ThisMobRank,FString AlterId)
{
	this->Setup(MobModel);
	this->Flyable = MobModel.Flyable;
	this->CharacterObj.MobRank = ThisMobRank;
	
	this->CharacterObj.MobAlterId = AlterId;
	this->CharacterObj.MobId = MobModel.Id;
	this->MobClassId = MobModel.Id;
	
	this->CharacterObj.Name = MobModel.ChaName;
	this->CharacterObj.BasePropStar = MobModel.BaseProp;
	this->SetLevelTo(InitLevel);
	this->Side = ToSide;
	this->SecondWind = 0;
	this->MountsTypeRotate = MobModel.MountsTypeRotate;
	this->SoundDictionary = MobModel.SoundBaseDictionary;
	this->FightingWill = FFightingWill(MobModel.StartFightingWillLevel, MobModel.MaxFightingWillLevel);
	this->SetToArms(EArmState::Unarmed);

	for (FString CurTag : MobModel.Tag)
	{
		if (!this->CharacterObj.Tag.Contains(CurTag))
			this->CharacterObj.Tag.Add(CurTag);
	}


	if (this->GetLocalRole() == ENetRole::ROLE_Authority)
	{
		//部位
		for (FChaPart ChaPart : MobModel.Part)
		{
			this->CharacterObj.Part.Add(ChaPart);
		}
	}

	//添加默认的Buff，TODO：预设的buff是要读表来的
	for (const FTableAddBuffInfo BuffInfo : MobModel.InitBuff)
	{
		FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffInfo.Id);
		if (BuffModel.ValidBuffModel() == false) continue;
		this->AddBuff(FAddBuffInfo(this, this, BuffModel, BuffInfo.Stack, BuffInfo.Time, false, BuffInfo.Infinity));
	}

	GatherActorComponents();
	ConnectHitBoxToPart();
	GatherSubMeshes();

	//要隐藏的部位
	for (const FString HidePartId : MobModel.HideSightPartsOnCreate)
	{
		if (this->BodySightParts.Contains(HidePartId))
		{
			BodySightParts[HidePartId]->SetVisibility(false);
		}
	}

	//初始销毁的部位
	for (FString PartId : MobModel.ChaPartsNotDefaultActive)
	{
		//删除对应部位的装备
		FChaPart* Part = GetChaPartById(PartId);
		if (Part) UnwearEquipment(Part->PartType);
		//"删除"掉
		BreakChaPart(PartId);
	}

	//装备
	for (FMobEquipmentInfo InitEquipment : MobModel.InitEquipments)
	{
		if (FMath::RandRange(0.000f, 1.000f) < InitEquipment.Rating)
			WearEquipmentById(InitEquipment.EquipmentId);
	}

	FullyRestore();

	LastLocation = GetActorLocation();
	// UKismetSystemLibrary::PrintString(this,FString::FromInt(this->CurrentHP()),true,true,FLinearColor::Red,60);

	this->InteractWidgetVisible(false);

	if (ThisMobRank == EMobRank::Normal)
	{
		if (this->BreakSystemComponent)
			this->BreakSystemComponent->SetActiveBreakSystem(false);
	}
	else
	{
		if (this->BreakSystemComponent)
			this->BreakSystemComponent->SetActiveBreakSystem(true);
	}
}

void AAwCharacter::SetupAsPlayerCharacter(FString ClassInfoId, FString TypeId, int InitLevel)
{
	this->CharacterObj.ClassId = ClassInfoId;
	this->CharacterObj.TypeId = TypeId;
	this->PlayerClassId = ClassInfoId;
	FBattleClassModel BattleClass = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(ClassInfoId);
	this->Setup(BattleClass, TypeId);
	this->SetLevelTo(InitLevel);
	this->Flyable = false; //TODO:现在玩家总是不会在飞的，但愿以后也是如此
	this->SetToArms(EArmState::Unarmed);
	this->Side = 0;
	this->SecondWind = 2.0f;
	this->SoundDictionary = BattleClass.SoundBaseDictionary;

	this->MountsTypeRotate = BattleClass.MountsTypeRotate;

	for (FString CurTag : BattleClass.Tags)
	{
		if (!this->CharacterObj.Tag.Contains(CurTag))
			this->CharacterObj.Tag.Add(CurTag);
	}

	if (this->GetLocalRole() == ENetRole::ROLE_Authority)
	{
		InitPlayerChaPart();
	}

	//添加默认的Buff 
	for (const FTableAddBuffInfo BuffInfo : BattleClass.StartBuffInfo)
	{
		FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffInfo.Id);
		if (BuffModel.ValidBuffModel() == false) continue;
		this->AddBuff(FAddBuffInfo(this, this, BuffModel, BuffInfo.Stack, BuffInfo.Time, false, BuffInfo.Infinity));
	}

	//获取角色的攻击框和受击框，加入到列表里
	GatherActorComponents();
	ConnectHitBoxToPart();
	GatherSubMeshes();

	FullyRestore();

	LastLocation = GetActorLocation();

	//TODO: test
	// FWeaponModel WModel = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById("BigSword");
	// FEquippedWeaponSet ToWear = FEquippedWeaponSet(CharacterObj.TypeId, WModel);
	// this->WearWeapon(ToWear);
	// if (TypeId == "TypeA")
	// {
	// 	// 测试用 女战士装备
	// 	this->WearEquipment(UGameplayFuncLib::GetAwDataManager()->GetEquipmentById("Feather"));
	// 	this->WearEquipment(UGameplayFuncLib::GetAwDataManager()->GetEquipmentById("TestBra"));
	// 	this->WearEquipment(UGameplayFuncLib::GetAwDataManager()->GetEquipmentById("WarriorGlove"));
	// 	this->WearEquipment(UGameplayFuncLib::GetAwDataManager()->GetEquipmentById("WarriorBoot"));
	// }
	// if (TypeId == "TypeB")
	// {
	// 	// 测试用 男战士装备
	// 	// this->WearEquipment(UGameplayFuncLib::GetAwDataManager()->GetEquipmentById("Test_Warrior_Male"));
	// }

	//TODO 这个要搬到AsPlayer下面的
	if (UGameplayFuncLib::IsRogueMode())
	{
		WearRogueEquipments(ClassInfoId);
		const UAwRogueDataSystem* DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<
			UAwRogueDataSystem>();
		const FString WeaponId = DataSystem->GetCurrWeaponId(ClassInfoId);
		WearRogueWeapon(WeaponId);
	}
	else
	{
		if (UGameplayFuncLib::GetAwGameInstance()->RoleInfo.LoginTimes <= 0)
		{
			GiveNewRoleItems(UGameplayFuncLib::GetAwDataManager()->GetNewRoleItemsByCharacterType("TypeA"));
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.LoginTimes += 1;
		}
	}


	// 测试用
	CharacterObj.Name = "Awakener";

	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.MainCharacter = FAwCharacterInfo(
		this->CharacterObj.Name,
		this->CharacterObj.ClassId,
		this->CharacterObj.TypeId,
		this->CharacterObj.Level,
		this->CharacterObj.Buff,
		this->CharacterObj.WeaponSet,
		this->EquippedEquipments()
	);

	this->InteractWidgetVisible(false);

	UGameplayFuncLib::SaveGame();
}

void AAwCharacter::InitPlayerChaPart()
{
	//玩家角色必定只有4个部位
	TArray<TTuple<FString, EChaPartType>> CPart;
	CPart.Add(TTuple<FString, EChaPartType>(TEXT("Head"), EChaPartType::Head));
	CPart.Add(TTuple<FString, EChaPartType>(TEXT("Arm"), EChaPartType::Arm));
	CPart.Add(TTuple<FString, EChaPartType>(TEXT("Leg"), EChaPartType::Leg));
	CPart.Add(TTuple<FString, EChaPartType>(TEXT("Body"), EChaPartType::Body));
	for (int i = 0; i < 4; i++)
	{
		FChaPart Par = FChaPart();
		Par.Id = CPart[i].Get<0>();
		Par.PartType = CPart[i].Get<1>();
		Par.MeatType = EChaPartMeatType::Meat;
		Par.MaxDurability.Init(9999, 1);
		Par.CanBeDestroy = false;
		Par.Durability = Par.MaxDurability[0];
		this->CharacterObj.Part.Add(Par);
	}
}

// Called when the game starts or when spawned
void AAwCharacter::BeginPlay()
{
	Super::BeginPlay();
	if (!PlayerCharacterInData)
	{
		SetupCharacter();
	}
}

//由于本地2P开始发热色的BeginPlay会比PlayerController先跑
//所以把BeginPlay相关的功能手动放到Possess之后的函数里
void AAwCharacter::SetupCharacter()
{
	if (HaveSetupCharacter)return;
	HaveSetupCharacter = true;
	CameraComponent = Cast<UAwCameraComponent>(GetComponentByClass(UAwCameraComponent::StaticClass()));
	if (GetMesh())
		AwAnimInstance = Cast<UAwAnimInstance>(GetMesh()->GetAnimInstance());
	if (this->AwAnimInstance)
		this->AwAnimInstance->Character = this;

	if (this->GetLocalRole() != ENetRole::ROLE_Authority)
	{
		if (MobClassId != "")
		{
			const FMobModel MobModel = UGameplayFuncLib::GetAwDataManager()->GetMobModelById(MobClassId,CharacterObj.MobAlterId);
			if (MobModel.Id == MobClassId)
				SetupAsMonster(MobModel, this->CharacterObj.Level, this->Side,EMobRank::Normal,CharacterObj.MobAlterId);
		}
		else if (PlayerClassId != "")
		{
			const FBattleClassModel BattleClass = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(
				PlayerClassId);
			this->Setup(BattleClass, CharacterObj.TypeId);

			GatherActorComponents();
			GatherSubMeshes();

			//穿装备 TODO: 其实要的只是显示外观
			TArray<FString> EquipmentIdList;
			for (FEquipment ThisEquipment : CharacterObj.Equipments)
			{
				EquipmentIdList.Add(ThisEquipment.Id);
				//HideEquipmentAppearance(&ThisEquipment);
			}
			for (const FString EquipmentId : EquipmentIdList)
				this->WearEquipmentById(EquipmentId);
			RefreshEquipmentAppearance();
		}

		// if (OwnerPlayerController)
		// {
		// 	if (OwnerPlayerController->GetLocalRole() == ENetRole::ROLE_AutonomousProxy)
		// 	{
		// 		OwnerPlayerController->InitInClient(this);
		// 	}
		// }
	}

	GetActionComponent()->ChangeCharacterState(ECharacterActionState::Ground, true);
	if (UGameplayFuncLib::GetDataManager())
		MP_FreezeDur = UGameplayFuncLib::GetDataManager()->DebugConfig.MP_FreezeDur;
	if (UGameplayFuncLib::IsRogueMode() && UGameplayFuncLib::GetAwGameInstance() && this->IsPlayerCharacter() && !
		PlayerClassId.IsEmpty())
	{
		UAwRogueDataSystem* DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		if (DataSystem->GetCurBattleDataIsActive())
			DataSystem->PostLoadSaveDataInBattle(OwnerPlayerController->GetLocalPCIndex());
		else
			DataSystem->PostLoadSaveDataInHall(OwnerPlayerController->GetLocalPCIndex());
	}

	//重置所有的CharacterHitBox的状态
	ResetAllCharacterHitBoxes();


	//肉鸽下回满状态并且重算属性 非肉鸽只重新计算属性
	if (UGameplayFuncLib::IsRogueMode())
	{
		//this->FullyRestore();
		this->AttrRecheck();
	}
	else
	{
		this->AttrRecheck();
	}
	PreloadAssets();
	//UE_LOG(LogTemp,Log,TEXT("Character %s BeginPlay success"),*this->GetFullName());
}

void AAwCharacter::GainExp(const int Exp) const
{
	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
	{
		const int LevelUp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.GainExp(Exp);
		if (LevelUp > 0)
		{
			UKismetSystemLibrary::PrintString(UGameplayFuncLib::GetAwGameState()->GetMyCharacter(),
			                                  FString("Level Up").Append(FString::FromInt(LevelUp)),
			                                  true, true, FLinearColor::Yellow, 10);
			AGameModeBase* gm = UGameplayStatics::GetGameMode(GetWorld());
			AAwGameModeBase* agm = Cast<AAwGameModeBase>(gm);
			if (agm)
			{
				agm->AddLevelUpRemain(LevelUp);
			}
		}
		else
		{
			UKismetSystemLibrary::PrintString(UGameplayFuncLib::GetAwGameState()->GetMyCharacter(),
			                                  FString("Exp Gain: ").Append(FString::FromInt(Exp)),
			                                  true, true, FLinearColor::Gray, 5);
		}
	}
}

float AAwCharacter::GetExpPercent() const
{
	return this->CharacterObj.Exp * 1.000f / 100.000f;
}

void AAwCharacter::Tick(float DeltaTime)
{
	if (GotReady == false) return;
	Super::Tick(DeltaTime);

	if (GetAwAnimInstance())
		GetAwAnimInstance()->Tick(DeltaTime); //因为要管理动画暂停，所以只能先丢pause上面
	CheckOnGround();
	if (Paused == true) return;

	//dubug
	//if (IsPlayerCharacter())
	//{
	//UKismetSystemLibrary::PrintString(nullptr,"AP:"+FString::FromInt(CharacterObj.CurrentRes.AP),true,false,FColor::Green,DeltaTime);
	//}

	//
	this->SpeedInputAcceptance =
		(IsInMontageAction() ? FVector::ZeroVector : FVector::OneVector) + Notify_SpeedAcceptance;
	this->RotateInputAcceptance = (IsInMontageAction() ? 0 : 1) + Notify_RotateAcceptance;
	if (SpeedInputAcceptanceModifier.Active == true)
	{
		const float MoveInputAccept = this->SpeedInputAcceptanceModifier.Update(DeltaTime, false);
		if (IsInMontageAction() == false)
			this->SpeedInputAcceptance = FVector(MoveInputAccept, MoveInputAccept, SpeedInputAcceptance.Z);
	}
	this->SpeedInputAcceptance = FVector(
		FMath::Min(1.00f, this->SpeedInputAcceptance.X),
		FMath::Min(1.00f, this->SpeedInputAcceptance.Y),
		FMath::Min(1.00f, this->SpeedInputAcceptance.Z)
	);
	//UKismetSystemLibrary::PrintString(this, FString("Speed ").Append(Notify_SpeedAcceptance.ToString()));

	if (this->NpcInfo.IsNpc())
	{
		this->NpcInfo.Personality.Update(DeltaTime);
	}

	if (this->ActionControlState.EqualsTo(GetActionControlState()) == false)
	{
		this->ActionControlState = *GetActionControlState();
		//AttrRecheck();
	}

	TArray<UTimelineNode*> TimelineNodes;
	for (int i = 0; i < CharacterObj.Buff.Num(); i++)
	{
		if (CharacterObj.Buff[i].Model.ValidBuffModel())
		{
			TTuple<FBuffObj, TArray<UTimelineNode*>> RunResult = CharacterObj.Buff[i].OnTick(DeltaTime);
			if (!CharacterObj.Buff.IsValidIndex(i)) continue;
			CharacterObj.Buff[i] = RunResult.Get<0>();
			TimelineNodes.Append(RunResult.Get<1>());
		}
	}

	int BIndex = 0;
	while (BIndex < this->CharacterObj.Buff.Num())
	{
		if (this->CharacterObj.Buff[BIndex].ToBeRemoved == true)
		{
			this->CharacterObj.Buff.RemoveAt(BIndex);
		}
		else
		{
			BIndex++;
		}
	}

	CharacterObj.Buff.Sort([](const FBuffObj& B1, const FBuffObj& B2)
	{
		return B1.Model.Priority < B2.Model.Priority;
	});

	//视觉特效
	int VIndex = 0;
	while (VIndex < PlayingFX.Num())
	{
		bool AtLeastOne = false;
		for (TTuple<USceneComponent*, UParticleSystemComponent*> Vfx : PlayingFX[VIndex].VFX)
		{
			if (Vfx.Value == nullptr)
			{
				if (PlayingFX[VIndex].PlayOnce == false)
				{
					UParticleSystem* Template = LoadObject<UParticleSystem>(
						nullptr, *UResourceFuncLib::GetAssetPath(PlayingFX[VIndex].VFXPath));
					if (Template)
					{
						Vfx.Value = UGameplayStatics::SpawnEmitterAttached(
							Template, Vfx.Key, NAME_None, FVector::ZeroVector, FRotator::ZeroRotator,
							FVector::OneVector, EAttachLocation::KeepRelativeOffset,
							true, EPSCPoolMethod::AutoRelease, true);
					}
					AtLeastOne = true;
				}
			}
			else
			{
				AtLeastOne = true;
			}
		}
		if (AtLeastOne == false && PlayingFX[VIndex].PlayOnce == true)
		{
			PlayingFX.RemoveAt(VIndex);
		}
		else
		{
			VIndex++;
		}
	}

	//处理所有挨揍
	while (0 < this->ToBeDamaged.Num())
	{
		UDamageManager::DoDamage(ToBeDamaged[0].Attacker, ToBeDamaged[0].Defender, ToBeDamaged[0].DamageInfo);
		ToBeDamaged.RemoveAt(0);
	}

	if (TimelineNodes.Num())
	{
		for (int i = 0; i < TimelineNodes.Num(); i++)
		{
			UGameplayFuncLib::GetTimelineManager()->AddNode(TimelineNodes[i]);
		}
	}

	if (InSecondWind())
	{
		this->SecondWind -= DeltaTime * SecWindLoseTimes;
		if (SecondWind <= 0)
		{
			PreorderActionByMontageState(ECharacterMontageState::Dead);
			OnDead();
		}
	}
	if (this->Dead(true))
	{
		this->StopAttaching();
		this->CharacterAttachment.RemoveAllAttacherOnMe();
	}

	//延长移动数据
	if (ExtendMoveInfo.Distance > 0)
	{
		ExtendMoveInfo.Distance -= GetMoveComponent()->ThisTickXYMoved;
	}
	if (ExtendMoveInfo.Duration > 0)
	{
		ExtendMoveInfo.Duration -= DeltaTime;
	}

	// if (this->SpeedInputAcceptanceModifier.Active)
	// {
	// 	const float MoveInputAccept = this->SpeedInputAcceptanceModifier.Update(DeltaTime, false);
	// 	
	// 	this->SpeedInputAcceptance = FVector(
	// 		FMath::Min(SpeedInputFromAction.X, MoveInputAccept),
	// 		FMath::Min(SpeedInputFromAction.Y, MoveInputAccept),
	// 		SpeedInputFromAction.Z
	// 	);
	//
	// 	UKismetSystemLibrary::PrintString(this, FString("Move Acceptance")
	// 		.Append(FString::SanitizeFloat(SpeedInputAcceptanceModifier.TimeElapsed)).Append(" / ")
	// 		.Append(FString::SanitizeFloat(MoveInputAccept)).Append(" || ")
	// 		.Append(SpeedInputFromAction.ToString()).Append(" || "),
	// 		true, true, FLinearColor::Green, 20);
	// }
	//
	//处理动作命中，但是绝对不会处理掉已经“无效”的伤害，因为CanHit<=0证明了不能再打
	CheckForActionHit();
	//外发光
	int i = 0;
	while (i < this->GlowExtents.Num())
	{
		if (GlowExtents[i].TimeElapsed >= GlowExtents[i].TotalTime)
		{
			GlowExtents.RemoveAt(i);
		}
		else
		{
			GlowExtents[i].TimeElapsed += DeltaTime;
			i++;
		}
	}

	this->SetGlowExtent(this->CurrentGlowExtends(), this->CurrentGlowColor());

	//战斗状态
	if (InWarDuration > 0)
	{
		InWarDuration -= DeltaTime;
	}

	//小怪死亡检测删除
	if (this->MobClassId.IsEmpty() == false && Dead())
	{
		if (MobRemoveAfterSec > 0)
			MobRemoveAfterSec -= DeltaTime;
		else
			this->Destroy();

		if (MobDeadFxAfterSec > 0)
			MobDeadFxAfterSec -= DeltaTime;
		if (MobDeadFxAfterSec <= 0.0f)
		{
			StartDissolve();
			TickChangeDissolve(DeltaTime);
		}
	}

	//挨揍记录
	int BeOffendedLoop = 0;
	while (BeOffendedLoop < this->BeOffendedRec.Num())
	{
		if (BeOffendedRec[BeOffendedLoop] > 0)
		{
			BeOffendedRec[BeOffendedLoop] -= DeltaTime;
			BeOffendedLoop += 1;
		}
		else
		{
			BeOffendedRec.RemoveAt(BeOffendedLoop);
		}
	}

	// UE_LOG(LogTemp,Log,TEXT("X : %f, Y : %f"),GetRightStickInput(EGameControlState::Game).X,GetRightStickInput(EGameControlState::Game).Y);

	Lived += DeltaTime;

	Tick_RecoveryChaProp(DeltaTime);

	if (OnGround())
	{
		if (!GetActionComponent()->CurrAction()->Id.Contains("Air_Dodge"))
			SetCurAirDodgePointToMax();
		// OnGroundTimer += DeltaTime;
		// if (OnGroundTimer >= 0.1f)
		// 	SetCurAirDodgePointToMax();
	}
	// else
	// 	OnGroundTimer = 0;
}

void AAwCharacter::AddHitRecord(FOffenseHitRecord Record) const
{
	GetAttackHitComponent()->AddHitRecord(Record);
}

bool AAwCharacter::GetHitBoxInJustDefense(FString HitBoxId)
{
	for (FDefenseInfo Info : DefendingInfo)
	{
		if (Info.CharacterHitBoxId.Contains(HitBoxId))
		{
			if (Info.bJustDenfense)
				return true;
		}
	}
	return false;
}

void AAwCharacter::AddFollowingEnemy(AAwCharacter* Enemy)
{
	FollowingEnemys.Add(Enemy);
}

void AAwCharacter::RemoveFollowingEnemy(AAwCharacter* Enemy)
{
	if (FollowingEnemys.Contains(Enemy))
		FollowingEnemys.Remove(Enemy);
}

void AAwCharacter::CheckForActionHit()
{
	TMap<AActor*, FActorBeHitCheckerArray> ThisTickTouch = ThisTickValidActionHitInfo();

	//遍历每条有效碰撞信息
	for (const TTuple<AActor*, FActorBeHitCheckerArray> Info : ThisTickTouch)
	{
		// AAwCharacter* Target = Cast<AAwCharacter>(Info.Key);
		//如果是一个角色，就这样干，否则么…………调用里面那个OnAttackHit，并追加记录就行了
		for (const FActorBeHitChecker Checker : Info.Value.Checkers)
		{
			if (Info.Key)
			{
				const UBeCaughtHitBox* HitBox = Cast<UBeCaughtHitBox>(Checker.CaughtActorInfo.CaughtHitBoxData);
				if (!HitBox) continue; //没有受击框就打不了
				if (HitBox->Active == false)
				{
					UKismetSystemLibrary::PrintString(this, "Some hit box is not active");
					continue;
				}

				FOffenseInfo OInfo = GetOffenInfoWithWeaponPower(Checker.OffenseInfo,
				                                                 Checker.CaughtActorInfo.BeCaughtActor->
				                                                         GetActorLocation());
				// OInfo.AttackInfo.RotateByDegree(this->GetActorRotation().Yaw, this->GetActorLocation(), Checker.CaughtActorInfo.BeCaughtActor->GetActorLocation());	//动作的在这里进行方向旋转
				UOffenseManager::DoOffense(GetAttackHitComponent(), OInfo, Checker.CaughtActorInfo, this, true);
			}
		}
	}
}

FOffenseInfo AAwCharacter::GetOffenInfoWithWeaponPower(FOffenseInfo OInfo, const FVector& TargetLocation) const
{
	OInfo.AttackInfo.DamagePower = OInfo.AttackInfo.DamagePower * ((
		this->CharacterObj.WeaponSet.MainHand.Model.AttackPower * OInfo.AttackInfo.WeaponEffect.MainHandRate +
		this->CharacterObj.WeaponSet.OffHand.Model.AttackPower * OInfo.AttackInfo.WeaponEffect.OffHandRate
	) + this->CharacterObj.CurProperty.PAttack);
	OInfo.AttackInfo.RotateByDegree(this->GetActorRotation().Yaw, this->GetActorLocation(), TargetLocation);
	//动作的在这里进行方向旋转
	return OInfo;
}

FOffendedCaughtResult AAwCharacter::CanBeOffended(FOffenseInfo OffenseInfo, UAttackHitManager* Attacker,
                                                  AAwCharacter* AttackerInCharge,
                                                  bool IncludeAttackerActionHitBoxes, bool SameSideFriendlyFire,
                                                  bool AllyFriendlyFire)
{
	//没有攻击判定框，就当做一次buff攻击
	if (!Attacker) return FOffendedCaughtResult(OffenseInfo);

	//阵营判断
	if (AttackerInCharge && AttackerInCharge->Side == this->Side && SameSideFriendlyFire == false)
		return
			FOffendedCaughtResult();
	if (AttackerInCharge && AttackerInCharge->IsEnemy(this) == false && AllyFriendlyFire == false)
		return
			FOffendedCaughtResult();

	//对方攻击框和记录信息判断
	TArray<FOffenseInfo> OInfos;
	if (AttackerInCharge && IncludeAttackerActionHitBoxes == true)
	{
		//如果有攻击负责人，把负责人所有的攻击信息加上
		OInfos.Append(AttackerInCharge->CurrentActionHitInfo);
	}
	if (OInfos.Contains(OffenseInfo) == false) OInfos.Add(OffenseInfo);
	TArray<FOffendedCaughtResult> CaughtInfos = Attacker->GetThisTickTargetCaughtInfo(this, OInfos);

	FOffendedCaughtResult Res;
	for (const FOffendedCaughtResult CInfo : CaughtInfos)
	{
		if (CInfo.Hit == true && Res.Hit == false)
		{
			Res = CInfo;
			continue;
		}
		if (
			CInfo.BeCaughtActorInfo.CaughtHitBoxData &&
			(
				!Res.BeCaughtActorInfo.CaughtHitBoxData ||
				Res.BeCaughtActorInfo.CaughtHitBoxData->Priority < CInfo.BeCaughtActorInfo.CaughtHitBoxData->Priority
			)
		)
		{
			Res = CInfo;
			continue;
		}
	}

	return Res;
}

void AAwCharacter::BeOffended(FOffenseInfo OffenseInfo, UAttackHitManager* Attacker,
                              AAwCharacter* AttackerInCharge, USceneComponent* BeHitBox, USceneComponent* FromAttackBox,
                              bool SameSideFriendlyFire, bool AllyFriendlyFire)
{
	//if (!HitBox || this->CharacterHitBoxes.Contains(HitBox) == false) return;	//没有找到对应挨揍的框

	//友伤避免
	if (
		AttackerInCharge && (
			(SameSideFriendlyFire == false && this->Side == AttackerInCharge->Side) ||
			(AllyFriendlyFire == false && AttackerInCharge->IsEnemy(this) == false)
		) && !OffenseInfo.AttackInfo.IsHeal
	)
		return;

	if (this->AIComponent && Attacker && Attacker->GetOwner())
	{
		AIComponent->OnBeOffended(Attacker->GetOwner()->GetActorTransform(), AttackerInCharge,
		                          OffenseInfo.AttackInfo.DamagePower.TotalDamage());
	}

	//--------------------测试代码，霸体buff-------------------------------
	if (CheckHasBuff("Test_Endure"))
	{
		OffenseInfo.AttackInfo.DefenderActionChange.Priority = -1;
	}
	//--------------------测试代码，霸体buff-------------------------------

	//UCharacterHitBox* HitBox = Cast<UCharacterHitBox>(BeHitBox);
	UCharacterHitBoxData* BeHitBoxData = BeHitBox ? BeHitBox->GetAssetUserData<UCharacterHitBoxData>() : nullptr;

	OnCharacterBeOffenedDelegate.Broadcast(this, OffenseInfo, Attacker, AttackerInCharge, BeHitBox, FromAttackBox,
	                                       SameSideFriendlyFire, AllyFriendlyFire);

	//先找找看防御信息
	const FString HitBoxId = BeHitBoxData ? BeHitBoxData->Id : "";
	int DefIndex = -1;
	if (HitBoxId.IsEmpty() == false)
	{
		for (int i = 0; i < this->DefendingInfo.Num(); i++)
		{
			if (DefendingInfo[i].CharacterHitBoxId.Contains(HitBoxId))
			{
				DefIndex = i;
				break;
			}
		}
	}
	FDefenseInfo DefInfo = DefIndex >= 0
		                       ? DefendingInfo[DefIndex]
		                       : (BeHitBoxData ? BeHitBoxData->DefaultDefenseInfo : FDefenseInfo());

	//如果攻击信息启动，说明有攻击，就可能造成动作变化和伤害 
	if (OffenseInfo.AttackInfo.Active)
	{
		FVector HitLocation = BeHitBox ? BeHitBox->GetComponentLocation() : FVector::ZeroVector;
		FVector HitLocOffset;
		FVector HitNormal = BeHitBox ? BeHitBox->GetComponentRotation().Vector() : FVector::ZeroVector;
		if (FromAttackBox && BeHitBoxData)
		{
			UGameplayFuncLib::GetHitResultBetweenSceneComponent(BeHitBox, FromAttackBox, HitLocation, HitNormal);
		}
		HitLocOffset = HitLocation + OffenseInfo.OffensePosOffset;
		if (BeHitBox) HitLocOffset -= BeHitBox->GetComponentLocation();

		//伤害处理
		FDamageInfo DInfo = FDamageInfo::FromAttackAndDefense(
			BeHitBox, OffenseInfo.AttackInfo, DefInfo, HitLocOffset
		);

		DInfo.Attacker = AttackerInCharge;
		//执行完美闪避时的额外执行事件		
		if (DInfo.BeDodged)
		{
			TArray<UTimelineNode*> DodgeTimeline;
			for (auto OnJustDodge : BeHitBoxData->AsJustDodge.OnSuccess)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(OnJustDodge);
				if (IsValid(Func) == false) continue;;
				struct
				{
					AAwCharacter* Dodger = nullptr;
					FDamageInfo DamageInfo = FDamageInfo();
					TArray<FString> Params;
					UTimelineNode* Result = nullptr;
				} FuncParam;
				FuncParam.Dodger = this;
				FuncParam.DamageInfo = DInfo;
				FuncParam.Params = OnJustDodge.Params;
				this->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result) DodgeTimeline.Add(FuncParam.Result);
			}
			if (DodgeTimeline.Num())
			{
				for (int i = 0; i < DodgeTimeline.Num(); i++)
				{
					UGameplayFuncLib::GetTimelineManager()->AddNode(DodgeTimeline[i]);
				}
			}
		}

		//临时，设置在怪物攻击怪物时，伤害*0.2
		if (!this->UnderPlayerControl() && AttackerInCharge && !AttackerInCharge->UnderPlayerControl() && !
			UGameplayFuncLib::IsRogueMode())
		{
			if (DInfo.DamageType == EDamageType::DirectDamage || DInfo.DamageType == EDamageType::PeriodDamage ||
				DInfo.DamageType == EDamageType::ReflectDamage || DInfo.DamageType == EDamageType::SpecDamage)
				DInfo.DamagePower = DInfo.DamagePower * 0.2;
		}

		UDamageManager::DoDamage(AttackerInCharge, this, DInfo);

		//为双方开启临时的Cancel点
		if (AttackerInCharge)
		{
			for (FCancelTagInfo TempCancelPoint : DInfo.AttackerActionChange.TemporaryCancelPoints)
			{
				AttackerInCharge->AddCancelTag(ECancelTagType::Temporary, TempCancelPoint.CancelPointIndex,
				                               TempCancelPoint.Duration);
			}
		}
		for (FCancelTagInfo TempCancelPoint : DInfo.DefenderActionChange.TemporaryCancelPoints)
		{
			this->AddCancelTag(ECancelTagType::Temporary, TempCancelPoint.CancelPointIndex, TempCancelPoint.Duration);
		}

		//改变双方的动作，是否会落马等，由对应动作中的AnimNotify来执行
		if (AttackerInCharge)
		{
			if (DInfo.AttackerActionChange.HitStun.Active)
			{
				FForceMoveInfo HitStun = DInfo.AttackerActionChange.HitStun;
				HitStun.Velocity *= AttackerInCharge->CharacterObj.CurProperty.BeStrikeRate;
				HitStun.InSec *= AttackerInCharge->CharacterObj.CurProperty.BeStrikeRate;
				if (!HitStun.Velocity.IsNearlyZero() && HitStun.InSec > 0)
				{
					AttackerInCharge->LastIncomingForce = HitStun.Velocity;
				}
				DInfo.AttackerActionChange.HitStun = HitStun;
			}
			for (FString ABeep : DInfo.AttackerActionChange.AchievementBeep)
			{
				AttackerInCharge->AchievementSignalBeep(ABeep);
			}
			FActionParam AttackerAP = FActionParam();
			AttackerAP.Active = true;
			AttackerAP.PriorityDistance = OffenseInfo.AttackInfo.AttackerActionChange.Priority - DefInfo.
				AttackerActionChange.Priority;
			AttackerInCharge->PreorderActionByActionChangeInfo(DInfo.AttackerActionChange, AttackerAP);
		}

		this->LastIncomingForce = OffenseInfo.AttackInfo.DamageIncomeVector; //默认最后收到的力是来自攻击的
		if (DInfo.DefenderActionChange.HitStun.Active)
		{
			FForceMoveInfo HitStun = DInfo.DefenderActionChange.HitStun;
			//TODO 现在受击方向会根据打点和目标ActorLocation变化，如果hand feeling很不对，就删除下面这个RotateAngleAxis
			//HitStun.Velocity = HitStun.Velocity.RotateAngleAxis(LastIncomingForce.Rotation().Yaw, FVector::UpVector);
			HitStun.Velocity *= this->CharacterObj.CurProperty.BeStrikeRate;
			HitStun.InSec *= this->CharacterObj.CurProperty.BeStrikeRate;
			if (!HitStun.Velocity.IsNearlyZero() && HitStun.InSec > 0)
			{
				this->LastIncomingForce = HitStun.Velocity; //如果有击退就会发生变化
			}
			DInfo.DefenderActionChange.HitStun = HitStun;
		}
		//TODO 下面加上挨打转向的特殊处理
		if (DInfo.DefenderActionChange.ChangeMethod == EActionChangeMethod::ToMontageState &&
			(DInfo.DefenderActionChange.ToState == ECharacterMontageState::Blow || DInfo.DefenderActionChange.ToState ==
				ECharacterMontageState::Hurt))
		{
			float RotTo;
			if (DInfo.DefenderActionChange.HitStun.Active)
			{
				FVector Velocity = DInfo.DefenderActionChange.HitStun.Velocity;
				RotTo = Velocity.RotateAngleAxis(180, FVector::UpVector).Rotation().Yaw;
			}
			else
			{
				FVector Forward = Attacker->GetOwner()->GetActorForwardVector();
				RotTo = Forward.RotateAngleAxis(180, FVector::UpVector).Rotation().Yaw;
			}
			// UKismetSystemLibrary::PrintString(this, FString("   Rot = ").Append(FString::SanitizeFloat(RotTo)),
			// 	true, true, FLinearColor::Yellow, 30);
			this->GetMoveComponent()->BeOffendedForceRotate = FBeOffendedForceRotate(RotTo);
		}
		//TODO 上面是挨打转向的特殊处理

		for (FString ABeep : DInfo.DefenderActionChange.AchievementBeep)
		{
			this->AchievementSignalBeep(ABeep);
		}
		FActionParam DefenderAP = FActionParam();
		DefenderAP.Active = true;
		DefenderAP.PriorityDistance = DefInfo.DefenderActionChange.Priority - OffenseInfo.AttackInfo.
			DefenderActionChange.Priority;
		if (IsPlayerCharacter() && !DInfo.IsHeal)
			this->GetAwAnimInstance()->TerminateFreeze(); //受击结束卡帧
		// 受击动画
		this->PreorderActionByActionChangeInfo(DInfo.DefenderActionChange, DefenderAP);
		// 受击手柄振动
		if (DInfo.DefenderActionChange.ChangeMethod == EActionChangeMethod::ToMontageState)
		{
			if (DInfo.DefenderActionChange.ToState == ECharacterMontageState::Hurt)
				UPlayForceFeedBack::Play(HurtForceFeedBackInfo, this->GetMesh());
			if (DInfo.DefenderActionChange.ToState == ECharacterMontageState::Blow)
				UPlayForceFeedBack::Play(BlowForceFeedBackInfo, this->GetMesh());
		}

		if (DInfo.DefenderActionChange.FreezeTime > 0 && this->MeshShocker)
		{
			//抖动试试看
			MeshShocker->SetFreeze(DInfo.DefenderActionChange.FreezeTime,
			                       FVector2D(OffenseInfo.AttackInfo.DamageIncomeVector.X,
			                                 OffenseInfo.AttackInfo.DamageIncomeVector.Y)
			);
		}

		//震屏，只有攻击方是玩家的才会
		if (AttackerInCharge && UGameplayFuncLib::GetAwGameState() && UGameplayFuncLib::GetAwGameState()->
			GetMyCharacter() == AttackerInCharge)
		{
			if (OffenseInfo.AttackInfo.ShakeInfo.Shake && this->GetWorld())
			{
				//只有用了防守方的DefenseInfo，才会播放震动（因为震动是DefenseInfo的数据嘛）
				const FVector FinalPos = OffenseInfo.AttackInfo.ShakeInfo.Offset + this->GetActorLocation();
				APlayerCameraManager::PlayWorldCameraShake(
					this->GetWorld(),
					OffenseInfo.AttackInfo.ShakeInfo.Shake, FinalPos,
					OffenseInfo.AttackInfo.ShakeInfo.FullShockRange,
					OffenseInfo.AttackInfo.ShakeInfo.LoseShockRange,
					OffenseInfo.AttackInfo.ShakeInfo.FallOff,
					OffenseInfo.AttackInfo.ShakeInfo.DoRotate
				);
			}
			// 攻击命中手柄振动，只有玩家的才会
			UPlayForceFeedBack::Play(OffenseInfo.AttackInfo.ForceFeedBackInfo, AttackerInCharge->GetMesh());
		}
		// 防御正常，手柄振动，只有玩家的才会
		if (this->IsPlayerCharacter())
			UPlayForceFeedBack::Play(DefInfo.ForceFeedBackInfo, this->GetMesh());

		if (AwPhysicalAnimComponent && AwPhysicalAnimComponent->IsActive())
		{
			AwPhysicalAnimComponent->ApplyPhyscialAnimBlendByDamageInfo(
				DInfo, BeHitBoxData ? BeHitBoxData->HitPhysicalAnimBlendRate : 0.f);
		}


		BeOffendedRec.Add(5.0f);

		//播放对应的特效（TODO：还未确定怎么做），在HitBox播放受击方的特效
		//FTransform HitTrans = BeHitBox ? BeHitBox->GetComponentTransform() : FTransform::Identity;
		FTransform HitTrans = FTransform(HitNormal.ToOrientationQuat(), HitLocation, FVector(1, 1, 1));
		//HitTrans.SetLocation(HitLocation + HitNormal * 5.0f);
		//HitTrans.SetRotation(HitNormal.ToOrientationQuat());
		//命中被闪避时不播放特效和音效
		if (UGameplayFuncLib::GetHitVFX(DInfo.AttackerActionChange) && BeHitBoxData && !DInfo.BeDodged)
			UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(DInfo.AttackerActionChange), HitTrans,
			                                      true, true);
		if (UGameplayFuncLib::GetHitSFX(DInfo.AttackerActionChange) && BeHitBoxData && !DInfo.BeDodged)
			UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(DInfo.AttackerActionChange), HitLocation,
			                                    HitNormal.Rotation());
		if (UGameplayFuncLib::GetHitVFX(DInfo.DefenderActionChange) && BeHitBoxData)
			UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(DInfo.DefenderActionChange), HitTrans,
			                                      true, true);
		if (UGameplayFuncLib::GetHitSFX(DInfo.DefenderActionChange) && BeHitBoxData)
			UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(DInfo.DefenderActionChange), HitLocation,
			                                    HitNormal.Rotation());
	}

	//如果有Catch，就要Catch一下
	if (AttackerInCharge && OffenseInfo.CatchInfo.Active == true)
	{
		AttackerInCharge->CatchTarget(this, OffenseInfo, BeHitBox);
	}

	//添加对AI记录
	GetAIComponent()->AddOffsenseInfo(OffenseInfo, AttackerInCharge);

	//因为这次已经hit了，记录的是还能hit多少次，所以得CanHitTimes-1
	SetInWarDuration(1.0f);
	if (Attacker)
	{
		Attacker->AddHitRecord(
			FOffenseHitRecord(
				OffenseInfo.SourceId,
				OffenseInfo.Index,
				this,
				OffenseInfo.CanHitTimes - 1,
				OffenseInfo.HitSameTargetDelay
			)
		);
	}
}

FString AAwCharacter::GetNpcId()
{
	if (this->IsPlayerCharacter()) return NpcInfo.PlayerNpcId();
	if (this->NpcInfo.IsNpc()) return this->NpcInfo.Id;
	return FString();
}

void AAwCharacter::BreakChaPart(FString PartId)
{
	FChaPart* Part = GetChaPartById(PartId);
	if (!Part) return;

	Part->Active = false;
	TArray<TTuple<USceneComponent*, UCharacterHitBoxData*>> Parts = GetCharacterHitBoxByPartType(Part->PartType);
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> PInfo : Parts)
	{
		PInfo.Get<1>()->Active = false;
		PInfo.Get<1>()->DefaultActive = false;
		PInfo.Get<0>()->SetActive(PInfo.Get<1>()->Active);
	}

	//隐藏显示对应的尸块
	for (const FString HidePart : Part->HideBodySightPartsOnBroken)
	{
		if (this->BodySightParts.Contains(HidePart) && BodySightParts[HidePart])
			BodySightParts[HidePart]->SetHiddenInGame(true);
	}
	for (const FString ShowPart : Part->ShowBodySightPartsOnBroken)
	{
		if (this->BodySightParts.Contains(ShowPart) && BodySightParts[ShowPart])
			BodySightParts[ShowPart]->SetHiddenInGame(false);
	}
}

void AAwCharacter::RestoreChaPart(FString PartId, TArray<int> MaxDurability)
{
	FChaPart* Part = GetChaPartById(PartId);
	if (!Part) return;

	Part->Active = true;
	Part->MaxDurability = MaxDurability;
	TArray<TTuple<USceneComponent*, UCharacterHitBoxData*>> Parts = GetCharacterHitBoxByPartType(Part->PartType);
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> PInfo : Parts)
	{
		PInfo.Get<1>()->Active = true;
		PInfo.Get<1>()->DefaultActive = true;
		PInfo.Get<0>()->SetActive(PInfo.Get<1>()->Active);
	}

	//隐藏显示对应的尸块
	for (const FString HidePart : Part->HideBodySightPartsOnBroken)
	{
		if (this->BodySightParts.Contains(HidePart) && BodySightParts[HidePart])
			BodySightParts[HidePart]->SetHiddenInGame(false);
	}
	for (const FString ShowPart : Part->ShowBodySightPartsOnBroken)
	{
		if (this->BodySightParts.Contains(ShowPart) && BodySightParts[ShowPart])
			BodySightParts[ShowPart]->SetHiddenInGame(true);
	}
}

void AAwCharacter::CatchTarget(AAwCharacter* Target, FOffenseInfo Info, USceneComponent* HitBox)
{
	if (!HitBox || !Target) return;
	const UCharacterHitBoxData* HitBoxData = HitBox->GetAssetUserData<UCharacterHitBoxData>();
	if (!HitBoxData) return;

	const FActionInfo* UsingAction = CurrentAction();

	if (Info.CatchInfo.TargetAttachMe == false)
	{
		//我抓住别人，Attach到别人身上
		if (this->CharacterAttachment.AttachTarget != nullptr) return; //已经抓住人了就不能抓人了

		UAttachPoint* AtkPoint = this->GetAttachPointByName(Info.CatchInfo.CatchPointId, true);
		UAttachPoint* DefPoint = Target->GetAttachPointByName(HitBoxData->SeatPointId, false);
		if (AtkPoint && DefPoint)
		{
			if (this->AttachOnTarget(AtkPoint, DefPoint) == true)
			{
				//因为这次已经hit了，记录的是还能hit多少次，所以得CanHitTimes-1
				AddHitRecord(
					FOffenseHitRecord(
						UsingAction->Id,
						Info.Index,
						Target,
						Info.CanHitTimes - 1,
						Info.HitSameTargetDelay
					)
				);
			}
		}
	}
	else
	{
		//我抓到了别人，别人Attach到我身上
		UAttachPoint* AtkPoint = Target->GetAttachPointByName(Info.CatchInfo.TargetCaughtPointId, true);
		UAttachPoint* DefPoint = this->GetAttachPointByName(Info.CatchInfo.CatchPointId, false);
		if (AtkPoint && DefPoint)
		{
			if (this->CharacterAttachment.AttachPointHasBeenAttached(DefPoint) == true)
			{
				return; //已经被攀附了，就不能被攀附了
			}
			if (
				Target->CharacterAttachment.AttachTarget != nullptr &&
				Cast<AAwCharacter>(this->CharacterAttachment.AttachTarget->GetOwner()) != this
			)
			{
				this->StopAttaching();
			}
			if (Target->AttachOnTarget(AtkPoint, DefPoint) == true)
			{
				//因为这次已经hit了，记录的是还能hit多少次，所以得CanHitTimes-1
				AddHitRecord(
					FOffenseHitRecord(
						UsingAction->Id,
						Info.Index,
						Target,
						Info.CanHitTimes - 1,
						Info.HitSameTargetDelay
					)
				);
			}
		}
	}
}

void AAwCharacter::ActiveOffenseHitBox(FString BoxName)
{
	GetAttackHitComponent()->ActiveAttackHitBox(BoxName);
}

void AAwCharacter::StopOffenseHitBox(FString BoxName)
{
	GetAttackHitComponent()->DeactiveAttackBox(BoxName);
}

TMap<AActor*, FActorBeHitCheckerArray> AAwCharacter::ThisTickValidActionHitInfo()
{
	TMap<AActor*, FActorBeHitCheckerArray> Res;

	//拿到这帧可以命中的信息
	TArray<FBeCaughtActorInfo> CaughtActorInfos = GetAttackHitComponent()->ThisTickValidTarget(false, true, true);
	for (FBeCaughtActorInfo AInfo : CaughtActorInfos)
	{
		AActor* Target = AInfo.BeCaughtActor;
		//碰撞目标如果是个角色死亡了也无效
		const AAwCharacter* TargetGuy = Cast<AAwCharacter>(AInfo.BeCaughtActor);
		if (TargetGuy && TargetGuy->Dead() == true) continue;

		//从激活的“攻击信息”中遍历
		for (FOffenseInfo OInfo : this->CurrentActionHitInfo)
		{
			//首先碰到的碰撞盒得跟我这个攻击信息的碰撞盒子对得上
			if (OInfo.AttackHitBoxName.Contains(AInfo.AttackBoxName))
			{
				//如果命中记录存在，则下一个
				if (!GetAttackHitComponent()->TargetCanBeHitByHitRecord(Target, OInfo.SourceId, OInfo.Index)) continue;
				//如果并没有老的信息，那么就加上了，否则留下优先级最高的碰撞
				if (Res.Contains(Target) == false)
				{
					FActorBeHitCheckerArray CArr;
					CArr.Checkers.Add(FActorBeHitChecker(OInfo, AInfo));
					Res.Add(Target, CArr);
				}
				else
				{
					const int AtIndex = Res[Target].IndexOfOffenseInfo(OInfo);
					if (AtIndex >= 0)
					{
						//if (Res[Target].Checkers[AtIndex].CaughtActorInfo.BeHitBox->Priority < AInfo.BeHitBox->Priority)
						if (Res[Target].Checkers[AtIndex].CaughtActorInfo.CaughtHitBoxData->Priority < AInfo.
							CaughtHitBoxData->Priority)
						{
							Res[Target].Checkers[AtIndex].CaughtActorInfo = AInfo;
						}
					}
					else
					{
						Res[Target].Checkers.Add(FActorBeHitChecker(OInfo, AInfo));
					}
				}
			}
		}
	}
	return Res;
}

bool AAwCharacter::IsInMontageAction() const
{
	if (!ActionComponent) return false;
	return ActionComponent->IsInMontageAction();
}

void AAwCharacter::SetBlendSpaceAnimIndex(int ToIndex) const
{
	if (ActionComponent) ActionComponent->BlendSpaceAnimIndex = ToIndex;
}

void AAwCharacter::PlayAimBlendSpace(int Index) const
{
	if (AwAnimInstance)
		AwAnimInstance->PlayAimBlendSpace(Index);
}

void AAwCharacter::StopAimBlendSpace() const
{
	if (AwAnimInstance)
		AwAnimInstance->StopAimBlendSpace();
}

float AAwCharacter::GetActionSpeedRate()
{
	float AnimRate = 1;
	if (GetAwAnimInstance()->AnimRateScaleModifies.Num() > 0)
	{
		TArray<float> Values;
		GetAwAnimInstance()->AnimRateScaleModifies.GenerateValueArray(Values);
		AnimRate = Values.Last();
	}
	return AnimRate + (CharacterObj.GetActionSpeedRate() - 1);
}

void AAwCharacter::OnChangeAction(FActionInfo WasAction)
{
	//UKismetSystemLibrary::PrintString(this, FString("change action from ").Append(WasAction.Id).Append(" to ").Append(CurrentAction()->Id));
	//清空当前动作可以被切换动作的信息（TODO:当然这会有bug，因为Montage和State共存，所以……）
	this->DefendingInfo.Empty();

	//this->SpeedInputAcceptance = IsInMontageAction() ? FVector::ZeroVector : FVector::OneVector;
	//this->RotateInputAcceptance = IsInMontageAction() ? 0 : 1;
	this->SpeedInputAcceptanceModifier.Active = false;

	//重置所有的CharacterHitBox的状态
	ResetAllCharacterHitBoxes();

	//执行所有的Buff
	TArray<UTimelineNode*> TimelineNodes;
	for (int i = 0; i < this->CharacterObj.Buff.Num(); i++)
	{
		TTuple<FBuffObj, TArray<UTimelineNode*>> RunResult = CharacterObj.Buff[i].OnActionChange(WasAction);
		CharacterObj.Buff[i] = RunResult.Get<0>();
		TimelineNodes.Append(RunResult.Get<1>());
	}
	if (TimelineNodes.Num())
		for (int i = 0; i < TimelineNodes.Num(); i++)
			UGameplayFuncLib::GetTimelineManager()->AddNode(TimelineNodes[i]);

	//换了动作，所以这个动作的Hit信息重置了，这里只删除伤害信息，不负责删除碰撞信息
	GetAttackHitComponent()->ClearAllHitRecords();

	//元素触发点也跟动作变化而清空
	this->ElementalTriggerTag.Empty();

	//换动作关闭所有激活中的攻击框
	GetAttackHitComponent()->DeactiveAllAttackBox();

	//关闭瞄准操作
	GetCmdComponent()->InAiming = false;

	//关闭forceMove，如果有必要的话
	GetMoveComponent()->CheckForStopForceMoveOnActionChange();
}

void AAwCharacter::AttrRecheck()
{
	//Properties
	this->CharacterObj.AttrRecheck();

	//States
	this->ControlState = this->ActionControlState + this->BaseControlState + this->AttachControlState;

	if (this->CharacterObj.Buff.Num())
	{
		for (int i = 0; i < CharacterObj.Buff.Num(); i++)
		{
			this->ControlState = this->ControlState + CharacterObj.Buff[i].Model.ControlState;
		}
	}

	this->CharacterObj.CurProperty.MinValueForCharacter();
	this->CharacterObj.CurrentRes.MinValueForCharacter();
}

void AAwCharacter::SetEquipmentProp(FChaProp NewEquipmentProp)
{
	this->CharacterObj.EquipmentProp = NewEquipmentProp;
}

void AAwCharacter::ResetBaseProperty()
{
	this->CharacterObj.ResetBaseProperty();
}

void AAwCharacter::RemoveBuffById(FString BuffId)
{
	for (int i = 0; i < this->CharacterObj.Buff.Num(); i++)
		if (CharacterObj.Buff[i].Model.Id == BuffId)
			UBuffManager::RemoveBuff(CharacterObj.Buff[i], false);
}

void AAwCharacter::RemoveBuffByTag(FString BuffTag)
{
	for (int i = 0; i < this->CharacterObj.Buff.Num(); i++)
		if (CharacterObj.Buff[i].Model.Tags.Contains(BuffTag))
			UBuffManager::RemoveBuff(CharacterObj.Buff[i], false);
}

void AAwCharacter::RemoveAllBuff()
{
	for (int i = 0; i < this->CharacterObj.Buff.Num(); i++)
	{
		UBuffManager::RemoveBuff(CharacterObj.Buff[i], false);
	}
}

void AAwCharacter::BuffEventOnRoomStart(int RoomLevel, ERogueRoomType RoomType)
{
	TArray<UTimelineNode*> TimelineNodes;
	for (int i = 0; i < CharacterObj.Buff.Num(); i++)
	{
		if (CharacterObj.Buff[i].Model.ValidBuffModel())
		{
			TTuple<FBuffObj, TArray<UTimelineNode*>> RunResult;
			for (auto FunString : CharacterObj.Buff[i].Model.OnRogueRoomStart)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FunString);
				if (IsValid(Func) == false) continue;

				struct
				{
					FBuffObj BuffObj;
					int RoomLevel;
					ERogueRoomType RoomType;
					TArray<FString> Params;
					FBuffRunResult Result;
				} FuncParam;

				FuncParam.BuffObj = CharacterObj.Buff[i];
				FuncParam.RoomLevel = RoomLevel;
				FuncParam.RoomType = RoomType;
				FuncParam.Params = FunString.Params;

				ProcessEvent(Func, &FuncParam);
				CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
				if (FuncParam.Result.TimelineNode)
				{
					TimelineNodes.Add(FuncParam.Result.TimelineNode);
				}
			}
		}
	}
}

void AAwCharacter::BuffEventOnRoomEnd(int RoomLevel, ERogueRoomType RoomType)
{
	TArray<UTimelineNode*> TimelineNodes;
	for (int i = 0; i < CharacterObj.Buff.Num(); i++)
	{
		if (CharacterObj.Buff[i].Model.ValidBuffModel())
		{
			TTuple<FBuffObj, TArray<UTimelineNode*>> RunResult;
			for (auto FunString : CharacterObj.Buff[i].Model.OnRogueRoomEnd)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FunString);
				if (IsValid(Func) == false) continue;;

				struct
				{
					FBuffObj BuffObj;
					int RoomLevel;
					ERogueRoomType RoomType;
					TArray<FString> Params;
					FBuffRunResult Result;
				} FuncParam;

				FuncParam.BuffObj = CharacterObj.Buff[i];
				FuncParam.RoomLevel = RoomLevel;
				FuncParam.RoomType = RoomType;
				FuncParam.Params = FunString.Params;

				ProcessEvent(Func, &FuncParam);
				CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
				if (FuncParam.Result.TimelineNode)
				{
					TimelineNodes.Add(FuncParam.Result.TimelineNode);
				}
			}
		}
	}
}

FBuffObj* AAwCharacter::AddBuff(FAddBuffInfo BuffInfo)
{
	if (Dead())
	{
		return nullptr;
	}
	const int Index = UBuffManager::AddBuff(BuffInfo);
	if (!CharacterObj.Buff.IsValidIndex(Index))
	{
		return nullptr;
	}
	this->AttrRecheck();
	OnAddBuff(CharacterObj.Buff[Index]);
	return &CharacterObj.Buff[Index];
}

bool AAwCharacter::CheckHasBuff(FString ModelId)
{
	bool Res = false;
	for (auto Buff : CharacterObj.Buff)
	{
		if (Buff.Model.Id == ModelId)
		{
			Res = true;
			return Res;
		}
	}
	return Res;
}

TArray<FBuffObj*> AAwCharacter::GetBuff(FString ModelId, TArray<AAwCharacter*> ByCasters)
{
	TArray<FBuffObj*> Res;
	for (int i = 0; i < this->CharacterObj.Buff.Num(); i++)
	{
		if (
			CharacterObj.Buff[i].ToBeRemoved == false &&
			CharacterObj.Buff[i].Model.Id == ModelId &&
			(ByCasters.Num() <= 0 || ByCasters.Find(CharacterObj.Buff[i].Caster) >= 0)
		)
		{
			Res.Add(&CharacterObj.Buff[i]);
		}
	}
	return Res;
}

TArray<int> AAwCharacter::GetBuffIndexes(FString ModelId, TArray<AAwCharacter*> ByCasters)
{
	TArray<int> Res;
	for (int i = 0; i < this->CharacterObj.Buff.Num(); i++)
	{
		if (
			CharacterObj.Buff[i].ToBeRemoved == false &&
			CharacterObj.Buff[i].Model.Id == ModelId &&
			(ByCasters.Num() <= 0 || ByCasters.Find(CharacterObj.Buff[i].Caster) >= 0)
		)
		{
			Res.Add(i);
		}
	}
	return Res;
}

int AAwCharacter::GetBuffStackTotal(FString ModelId)
{
	int StackTotal = 0;
	for (FBuffObj Buff : CharacterObj.Buff)
		if (Buff.Model.Id == ModelId)
			StackTotal += Buff.Stack;

	return StackTotal;
}

void AAwCharacter::SetLevelTo(int ToLevel)
{
	this->CharacterObj.Level = FMath::Max(1, ToLevel);
	this->AttrRecheck();
}

void AAwCharacter::LevelUp(int Times)
{
	this->CharacterObj.Level = FMath::Max(1, this->CharacterObj.Level + Times);
	this->AttrRecheck();
}

void AAwCharacter::InstantKill(bool Directly, bool IsByDamage)
{
	if (this->Dead(true)) return;
	if (Directly) this->SecondWind = 0;

	if (IsByDamage)
	{
		FDamageInfo DamageInfo = FDamageInfo();
		DamageInfo.DamagePower = FDamageValue(999999);
		BeDamaged(DamageInfo);
	}
	else
	{
		this->CharacterObj.CurrentRes.HP = 0;
		FActionParam PreorderParam;
		PreorderParam.Degree = this->GetActorRotation().Yaw;
		PreorderParam.Active = true;
		PreorderActionByMontageState(ECharacterMontageState::Dead, PreorderParam);
		this->OnCharacterDeadDelegate.Broadcast(this);
		this->OnBeKilled();
	}
}

void AAwCharacter::SetInWarDuration(float Time)
{
	this->InWarDuration = FMath::Max(Time, InWarDuration);
}

void AAwCharacter::BeDamaged(FDamageInfo DamageInfo)
{
	if (DamageInfo.DamageType == EDamageType::OtherDamage)
	{
		return;
	}

	if (DamageInfo.BeDodged == true)
	{
		SetInWarDuration(4.0f); //闪避掉攻击时候进入战斗的时间长度（秒）);
		return; //闪避了就do nothing了
	}

	const bool WasSecondWind = this->InSecondWind();

	int ToDealDamage = FMath::Abs(DamageInfo.DamagePower.TotalDamage());
	this->CharacterObj.CurrentRes.HP -= ToDealDamage * (DamageInfo.IsHeal == false ? 1 : -1);
	this->CharacterObj.CurrentRes.HP = FMath::Clamp(CharacterObj.CurrentRes.HP, 0, this->CharacterObj.CurProperty.HP);
	int32 PartIndex = -1;
	if (DamageInfo.HitBoxData && DamageInfo.HitBoxData->BelongsToPart && DamageInfo.HitBoxData->BelongsToPart->Active ==
		true)
		PartIndex = GetChaPartIndexById(DamageInfo.HitBoxData->BelongsToPart->Id);

	if (PartIndex >= 0)
	{
		if (DamageInfo.HitBoxData && DamageInfo.HitBoxData->BelongsToPart && DamageInfo.HitBoxData->BelongsToPart->
			Active == true)
		{
			TArray<FString> PlayEffectOnBindPoint;
			//TODO 特效音效还是写死的
			FString VFXPath =
				"Temp/Effect/ParagonZ/FX_Greystone/Particles/Greystone/Abilities/Primary/FX/P_Greystone_Primary_Impact_R";
			FString SFXPath = "Audio/Sound_effect/Magic/Glass/Smash_MagicGlassBreak1";

			//对应部位的装备都会受损，注意，怪物的武器（视觉层）也是装备（逻辑层）
			for (int i = 0; i < CharacterObj.Equipments.Num(); i++)
			{
				if (CharacterObj.Equipments[i].AffectPart != DamageInfo.HitBoxData->BelongsToPart->PartType) continue;
				FEquipmentDurabilityModifyResult ModRes = CharacterObj.Equipments[i].ReduceDurability(
					this->CharacterObj.TypeId, 1);
				if (ModRes.Changed == true)
				{
					for (FEquipmentAppearancePart RApp : ModRes.RemoveAppearance)
					{
						if (PlayEffectOnBindPoint.Contains(RApp.BindPointIds[static_cast<int32>(GetArmState())]) ==
							false)
							PlayEffectOnBindPoint.Add(RApp.BindPointIds[static_cast<int32>(GetArmState())]);
					}
				};
			}
			//主手武器不会损坏 TODO: 2022-09-30的时候如果没有问题，就删除下列注释代码
			// if ( this->CharacterObj.WeaponSet.MainHand.IsNull() == false)
			// {
			// 	FEquipmentDurabilityModifyResult ModRes = CharacterObj.WeaponSet.MainHand.ReduceDurability(1, true);
			// 	if (ModRes.Changed == true)
			// 	{
			// 		for (FEquipmentAppearancePart RApp : ModRes.RemoveAppearance)
			// 		{
			// 			if (PlayEffectOnBindPoint.Contains(RApp.BindPointId) == false)
			// 				PlayEffectOnBindPoint.Add(RApp.BindPointId);
			// 		} 
			// 	};
			// }
			//副手只有盾牌会损坏
			if (DamageInfo.HitBoxData->BelongsToPart->PartType == EChaPartType::Shield && this->CharacterObj.WeaponSet.
				OffHand.IsNull() == false)
			{
				FEquipmentDurabilityModifyResult ModRes = CharacterObj.WeaponSet.OffHand.ReduceDurability(1, false);
				if (ModRes.Changed == true)
				{
					for (FEquipmentAppearancePart RApp : ModRes.RemoveAppearance)
					{
						if (PlayEffectOnBindPoint.Contains(RApp.BindPointIds[static_cast<int32>(GetArmState())]) ==
							false)
							PlayEffectOnBindPoint.Add(RApp.BindPointIds[static_cast<int32>(GetArmState())]);
					}
				};
			}
			if (PlayEffectOnBindPoint.Num() > 0)
			{
				for (FString BindPointName : PlayEffectOnBindPoint)
				{
					if (this->EquipmentBindPoints.Contains(BindPointName) && EquipmentBindPoints[BindPointName] !=
						nullptr)
					{
						UGameplayFuncLib::CreateVFXByPathAtLocation(
							VFXPath, this->EquipmentBindPoints[BindPointName]->GetComponentTransform());
						//UGameplayFuncLib::CreateVFXByPathAttached(VFXPath, this->EquipmentBindPoints[BindPointName], NAME_None, FTransform());
						UGameplayFuncLib::PlaySFXByPathAtLocation(
							SFXPath, this->EquipmentBindPoints[BindPointName]->GetComponentLocation(),
							this->EquipmentBindPoints[BindPointName]->GetComponentRotation());
						//UGameplayFuncLib::PlaySFXByPathAttached(SFXPath, this->EquipmentBindPoints[BindPointName], NAME_None, FVector(), FRotator());
					}
				}
				RefreshEquipmentAppearance();
			}
		}

		//部位耐久度受损
		int DurabilityLoses = (DamageInfo.DamagePower * this->CharacterObj.Part[PartIndex].Breakable).TotalDamage();
		if (DurabilityLoses > 0)
		{
			int BrokenTimes = this->CharacterObj.Part[PartIndex].ReduceDurability(DurabilityLoses);
			if (BrokenTimes > 0)
			{
				//解除AttachPoint上所有的人
				this->CharacterAttachment.RemoveCharacterOnPart(this->CharacterObj.Part[PartIndex].Id);

				//这个部位是否彻底摧毁了，要移除掉了
				bool FinalBroken = this->CharacterObj.Part[PartIndex].Broken();

				//执行对应的回调
				for (int FuncRunTimes = 0; FuncRunTimes < BrokenTimes; FuncRunTimes++)
				{
					for (FString BFunc : this->CharacterObj.Part[PartIndex].OnBroken)
					{
						FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(BFunc);
						UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
						if (Func)
						{
							struct
							{
								AAwCharacter* Character;
								FChaPart Part;
								bool FinalRemoved;
								TArray<FString> Params;
								UTimelineNode* Result;
							} FParam;
							FParam.Character = this;
							FParam.Part = this->CharacterObj.Part[PartIndex];
							FParam.FinalRemoved = FinalBroken;
							FParam.Params = FuncData.Params;
							this->ProcessEvent(Func, &FParam);
							if (FParam.Result) UGameplayFuncLib::GetTimelineManager()->AddNode(FParam.Result);
						}
					}
				}

				//如果这个部位坏掉了，就要清除出列表
				if (FinalBroken)
				{
					BreakChaPart(CharacterObj.Part[PartIndex].Id);
				}
			}
		}
	}

	//增加个人恩怨 TODO 目前个人恩怨度=伤害值
	if (this->NpcInfo.IsNpc() && DamageInfo.Attacker)
	{
		this->NpcInfo.Personality.AddDissatisfy(DamageInfo.Attacker->GetNpcId(), ToDealDamage);
	}

	//死亡判定
	const bool NowSecondWind = this->InSecondWind();
	const bool Dead = this->Dead();

	if (Dead == true || NowSecondWind == true)
	{
		FActionParam PreorderParam;
		PreorderParam.Degree = DamageInfo.DamageIncomeVector.Rotation().Yaw - this->GetActorRotation().Yaw;
		PreorderParam.Active = true;
		if (Dead == true)
		{
			OnDead();
		}
		PreorderActionByMontageState((Dead == true ? ECharacterMontageState::Dead : ECharacterMontageState::SecondWind),
		                             PreorderParam);
	}

	//如果曾经是濒死，现在不是了，并且没有死亡，就做个站起来的动作，就这么办吧，毕竟刚进入濒死也不能预设动作，所以么，就先这样了
	if (WasSecondWind == true && NowSecondWind == false && Dead == false)
	{
		PreorderActionByMontageState(ECharacterMontageState::GetUp);
	}

	//BreakSystem
	if (this->BreakSystemComponent)
		this->BreakSystemComponent->OnHit(DamageInfo);

	if (!DamageInfo.IsHeal && this->IsPlayerCharacter() && DamageInfo.DamagePower.TotalDamage() > 0)
	{
		OnCharacterBeDamagedDelegate.Broadcast(this, DamageInfo);
	}

	//跳数字
	//if(UGameplayFuncLib::GetAwDataManager()->DebugConfig.ShowPopText&&DamageInfo.FinalDamage()>0&&DamageInfo.DamageType!=EDamageType::OtherDamage)
	if (URogueGameSetting::GetRogueGameSettings()->GetShowDamageText() && ToDealDamage > 0 && DamageInfo.DamageType !=
		EDamageType::OtherDamage)
	{
		FString OnPlayer = this->IsPlayerCharacter() ? "Player" : (this->IsPlayersEnemy() ? "Enemy" : "NPC");
		FString AType = (DamageInfo.IsHeal == true) ? "Heal" : "Attack";
		FString AttackT = DamageInfo.Attacker
			                  ? (DamageInfo.Attacker->IsPlayerCharacter()
				                     ? "Player"
				                     : (DamageInfo.Attacker->IsPlayersEnemy() ? "Enemy" : "NPC"))
			                  : "SceneItem";
		FString WeakT = (!DamageInfo.HitBoxData || !DamageInfo.HitBoxData->BelongsToPart || DamageInfo.HitBoxData->
			                BelongsToPart->IsWeakPart()) ||
		                DamageInfo.IsCritical
			                ? "Weak"
			                : "Normal";
		FString RogueDamageTexId = "";
		if (UGameplayFuncLib::IsRogueMode())
		{
			WeakT = DamageInfo.IsCritical ? "Weak" : "Normal";
			RogueDamageTexId = DamageInfo.DamageType == EDamageType::RogueSeniorDamage ? "Senior" : "";
		}
		FString DamageElement = DamageInfo.Elemental == EChaElemental::Physical ? "Physical" : "Elemental";
		FString TextId = AttackT.Append("_").Append(AType).Append("_").Append(WeakT)
		                        .Append("_").Append(OnPlayer).Append("_").Append(DamageElement);
		if (!RogueDamageTexId.IsEmpty())
		{
			TextId.Append("_").Append(RogueDamageTexId);
		}

		if (UGameplayFuncLib::GetAwDataManager()->DebugConfig.PopTextInfo.Contains(TextId))
		{
			FPopTextLauncher TextLauncher = UGameplayFuncLib::GetAwDataManager()->DebugConfig.PopTextInfo[TextId];
			int PopPriority = TextLauncher.Priority;
			int PopSize = TextLauncher.Size;
			FLinearColor PopColor = FLinearColor::FromSRGBColor(TextLauncher.Color);
			FVector RandomOffset = FVector(UKismetMathLibrary::RandomFloat() * 10,
			                               UKismetMathLibrary::RandomFloat() * 10,
			                               UKismetMathLibrary::RandomFloat() * 10);
			FVector PopPos = DamageInfo.HitBox ? DamageInfo.HitBox->GetComponentLocation() : this->GetActorLocation();
			PopPos += DamageInfo.HitLocationOffset + RandomOffset;
			//UKismetSystemLibrary::PrintString(this,DamageInfo.HitLocationOffset.ToString(),true,true,FColor::Red,10.f);
			FString PopText = FString::FromInt(ToDealDamage);

			APopUpText* PopTextActor =
				UGameplayFuncLib::GetAwGameInstance()->PopText(
					PopPos, PopText, PopPriority, PopSize, PopColor
				);

			if (IsValid(PopTextActor) && IsValid(PopTextActor->WidgetComponent))
			{
				PopTextActor->WidgetComponent->SetInitialLayerZOrder(PopPriority - 100);
			}

			if (PopTextActor && UGameplayFuncLib::IsRogueMode() && PopTextActor->Implements<URogueInterface>())
			{
				TMap<FString, FString> InterfaceParams;
				InterfaceParams.Add("Target", OnPlayer);
				InterfaceParams.Add("Type", DamageInfo.IsHeal ? "Heal" : "Hurt");
				InterfaceParams.Add("IsCritical", DamageInfo.IsCritical ? "true" : "false");
				if (!RogueDamageTexId.IsEmpty())
				{
					InterfaceParams.Add("SeniorElement", "true");
				}
				InterfaceParams.Add("Element", UDataFuncLib::EnumToFString(DamageInfo.Elemental));
				IRogueInterface::Execute_GiveInfoMap(PopTextActor, InterfaceParams);
			}
		}
	}

	SetInWarDuration(DamageInfo.IsHeal ? 2.0f : 6.0f); //受治疗或者伤害进入战斗长度（秒）
}

void AAwCharacter::TickChangeDissolve(float DeltaTime)
{
	if (!Dissolve_Enable)
		return;

	const float MaxDuration = FMath::Max(Dissolve_AmountDuration, Dissolve_WidthDuration);
	if (Dissolve_Timer < MaxDuration)
	{
		Dissolve_Timer += DeltaTime;
		if (Dissolve_Timer > MaxDuration) Dissolve_Timer = MaxDuration;

		const float Amount = Dissolve_Timer > Dissolve_AmountDuration
			                     ? Dissolve_AmountTo
			                     : (Dissolve_AmountFrom + (Dissolve_AmountTo - Dissolve_AmountFrom) * Dissolve_Timer /
				                     Dissolve_AmountDuration);
		// const float Width = Dissolve_Timer > Dissolve_WidthDuration ? Dissolve_WidthTo :
		// 	(Dissolve_WidthFrom + (Dissolve_WidthTo - Dissolve_WidthFrom) * Dissolve_Timer / Dissolve_WidthDuration);

		// UKismetSystemLibrary::PrintString(this, FString::SanitizeFloat(Width));

		for (UMeshComponent* MeshComp : GetAllMeshComponents())
		{
			if (MeshComp)
			{
				const int MaterialNum = MeshComp->GetNumMaterials();
				for (int i = 0; i < MaterialNum; i++)
				{
					UMaterialInstanceDynamic* MaterialInstDyn = MeshComp->CreateDynamicMaterialInstance(i);
					float ToWidth = 0.05;
					MaterialInstDyn->GetScalarParameterValue(FName("ToWidth"), ToWidth);

					// UKismetSystemLibrary::PrintString(this, " --===> ToWidth: " + FString::SanitizeFloat(ToWidth));

					const float Width = Dissolve_Timer >= Dissolve_WidthDuration
						                    ? ToWidth
						                    : (Dissolve_WidthFrom + (ToWidth - Dissolve_WidthFrom) * Dissolve_Timer /
							                    Dissolve_WidthDuration);

					// if (i == 0 && MeshComp->GetName().Contains("CharacterMesh"))
					// 	UKismetSystemLibrary::PrintString(this, " --===> Width: " + FString::SanitizeFloat(Width));

					MaterialInstDyn->SetScalarParameterValue("Width", Width);
				}

				MeshComp->SetScalarParameterValueOnMaterials("Amount", Amount);
				// MeshComp->SetScalarParameterValueOnMaterials("Width", Width);
			}
		}
	}
}

void AAwCharacter::ShowTextBubble(FString Text, FString ChaName, float InSec)
{
	UGameplayFuncLib::GetAwGameInstance()->ShowTalkBubble(
		this, ChaName, Text, InSec
	);
}


bool AAwCharacter::IsPlayerCharacter() const
{
	if (PlayerCharacterInData)return true;
	return UGameplayFuncLib::GetAwGameState()
		       ? UGameplayFuncLib::GetAwGameState()->GetPlayerCharacters().Contains(this)
		       : false;
}

bool AAwCharacter::IsPlayersEnemy()
{
	return UGameplayFuncLib::GetAwGameState()
		       ? (
			       UGameplayFuncLib::GetAwGameState()->GetMyCharacter()
				       ? UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsEnemy(this)
				       : false
		       )
		       : false;
}

void AAwCharacter::PlayHitFX_Implementation(bool IsDamage, int DamValue, FTransform PlayTransform,
                                            EChaPartMeatType MeatType)
{
	//UGameplayStatics::SpawnEmitterAtLocation(this->GetWorld(), )
	//播放视觉特效和音频特效  //TODO:FX 将会移动到对应的动作相关的地方，根据预定的动作等来播放，比如挨打了是扑一声，格挡了是叮一声等。
	// if (DamageResult.HitVFX)
	// {
	// 	UGameplayStatics::SpawnEmitterAtLocation(this->GetWorld(), DamageResult.HitVFX, PlayTransform);
	// }
	// if (DamageResult.HitSFX)
	// {
	// 	UGameplayStatics::SpawnSoundAtLocation(this->GetWorld(), DamageResult.HitSFX, PlayTransform.GetLocation(), PlayTransform.GetRotation().Rotator());
	// }

	//根据伤害值算出伤害等级和对应肉质的特效，读取并调用
	if (IsDamage == true)
	{
		const int HurtRank = FMath::FloorToInt(FMath::Pow(DamValue * 1.000f / CharacterObj.CurProperty.HP, 0.420f) * 3);
		FString DefaultHurtKey;
		switch (MeatType)
		{
		default:
		case EChaPartMeatType::Meat: DefaultHurtKey = "Blood";
			break;
		case EChaPartMeatType::Metal: DefaultHurtKey = "Metal";
			break;
		case EChaPartMeatType::Skeleton: DefaultHurtKey = "BoneShard";
			break;
		case EChaPartMeatType::Void: DefaultHurtKey = "Dust";
			break;
		}
		const FString HurtKey = FString("ArtResource/VFX/CharacterDamaged/Particles/HurtEffect_").Append(DefaultHurtKey)
			.Append("_").Append(FString::FromInt(HurtRank));
		const FString HurtAssetPath = UResourceFuncLib::GetAssetPath(HurtKey);
		UParticleSystem* HurtParticleSystem = LoadObject<UParticleSystem>(nullptr, *HurtAssetPath);
		if (HurtParticleSystem)
		{
			UGameplayStatics::SpawnEmitterAtLocation(this->GetWorld(), HurtParticleSystem, PlayTransform);
		}
		//播放
		if (SoundDictionary.IsEmpty() == false && MouthPoint)
		{
			const FString HurtSoundPath = SoundDictionary.Append("Hurt");
			USoundBase* HurtSound = LoadObject<USoundBase>(nullptr, *UResourceFuncLib::GetAssetPath(HurtSoundPath));
			if (HurtSound)
			{
				UGameplayStatics::SpawnSoundAttached(HurtSound, GetMesh(), FName(MouthPoint->GetName()));
			}
		}
	}
}

void AAwCharacter::FullyRestore(bool Revive, bool Directly, bool RestoreMax)
{
	if (this->Dead() == true && Revive == true) this->CharacterObj.CurrentRes.HP = 1;

	this->AttrRecheck();

	this->CharacterObj.CurrentRes.MP = this->CharacterObj.CurProperty.MP; //this->MPMax;
	this->CharacterObj.CurrentRes.SP = this->CharacterObj.CurProperty.SP;
	this->CharacterObj.CurrentRes.AP = this->CharacterObj.CurProperty.AP;
	this->CharacterObj.CurrentRes.AirDodgePoint = this->CharacterObj.CurProperty.AirDodgePoint;
	if (Directly == true)
	{
		this->CharacterObj.CurrentRes.HP = this->CharacterObj.CurProperty.HP;
	}
	else
	{
		FOffenseInfo Offense = FOffenseInfo();
		Offense.AttackInfo = FAttackInfo();
		Offense.AttackInfo.DamagePower = FDamageValue(this->CharacterObj.CurProperty.HP);
		Offense.AttackInfo.IsHeal = true;
		Offense.AttackInfo.DamageSourceType = EAttackSource::None;
		Offense.AttackInfo.Elemental = this->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set,
		                                                  ESlotInWeaponObj::MainWeapon);
		Offense.SourceId = "System_Set_RestoreHealth";
		Offense.CanHitTimes = 1;
		this->BeOffended(Offense, GetAttackHitComponent(), this, nullptr);
	}
}

// UCharacterHitBox* AAwCharacter::GetCharacterHitBoxByName(FString TargetHitBoxName)
// {
// 	for (int i = 0; i < this->CharacterHitBoxes.Num(); i++)
// 	{
// 		if (CharacterHitBoxes[i]->GetName() == TargetHitBoxName)
// 		{
// 			return CharacterHitBoxes[i];
// 		}
// 	}
// 	return nullptr;
// }
TArray<FString> AAwCharacter::AllActiveCharacterHitBoxIds()
{
	TArray<FString> Res;
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo : this->CharacterHitBoxes)
	{
		const UCharacterHitBoxData* Data = HitBoxInfo.Get<1>();
		if (Data && Data->Active == true)
		{
			Res.Add(Data->Id);
		}
	}
	return Res;
}

TTuple<USceneComponent*, UCharacterHitBoxData*> AAwCharacter::GetCharacterHitBoxByName(FString TargetHitBoxName)
{
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo : this->CharacterHitBoxes)
	{
		const USceneComponent* Data = HitBoxInfo.Get<0>();
		if (Data && Data->GetName() == TargetHitBoxName)
		{
			return HitBoxInfo;
		}
	}
	return TTuple<USceneComponent*, UCharacterHitBoxData*>(nullptr, nullptr);
}

TArray<TTuple<USceneComponent*, UCharacterHitBoxData*>> AAwCharacter::GetCharacterHitBoxByPartType(
	EChaPartType PartType)
{
	TArray<TTuple<USceneComponent*, UCharacterHitBoxData*>> Res;
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo : this->CharacterHitBoxes)
	{
		const UCharacterHitBoxData* Data = HitBoxInfo.Get<1>();
		if (Data && Data->PartType == PartType)
		{
			Res.Add(HitBoxInfo);
		}
	}
	return Res;
}


void AAwCharacter::ResetAllCharacterHitBoxes()
{
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBox : this->CharacterHitBoxes)
	{
		if (HitBox.Get<1>())
		{
			HitBox.Get<1>()->Active = HitBox.Get<1>()->DefaultActive;
			if (HitBox.Get<0>())
			{
				HitBox.Get<0>()->SetActive(HitBox.Get<1>()->Active);
			}
		}
	}
	//TODO 即将干掉
	// for (UCharacterHitBox* Box : this->CharacterHitBoxes)
	// {
	// 	Box->Active = Box->DefaultActive;
	// } 
}

void AAwCharacter::SetAllCharacterHitBoxActive(bool ToActive)
{
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBox : this->CharacterHitBoxes)
	{
		if (HitBox.Get<1>())
		{
			HitBox.Get<1>()->Active = ToActive;
			HitBox.Get<0>()->SetActive(HitBox.Get<1>()->Active);
		}
	}
	//TODO 即将干掉
	// for (UCharacterHitBox* Box : this->CharacterHitBoxes)
	// {
	// 	Box->Active = ToActive;
	// } 
}

void AAwCharacter::SetJustDodge(FJustDodgeInfo DodgeInfo)
{
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBox : this->CharacterHitBoxes)
	{
		if (HitBox.Get<1>())
		{
			HitBox.Get<1>()->AsJustDodge = DodgeInfo;
			if (HitBox.Get<1>()->Type == ECharacterHitBoxType::JustDodge)
			{
				HitBox.Get<1>()->Active = DodgeInfo.Active;
				HitBox.Get<0>()->SetActive(HitBox.Get<1>()->Active);
			}
		}
	}
	//TODO 即将干掉
	// for (UCharacterHitBox* Box : this->CharacterHitBoxes)
	// {
	// 	Box->AsJustDodge = DodgeInfo;
	// } 
}


USceneComponent* AAwCharacter::GetAttackHitBoxByName(FString TargetHitBoxName) const
{
	UPrimitiveComponent* PComp = GetAttackHitComponent()->GetAttackBoxByName(TargetHitBoxName);
	if (PComp) return Cast<USceneComponent>(PComp);
	return nullptr;
}


FChaPart* AAwCharacter::GetChaPartById(FString Id)
{
	for (int i = 0; i < this->CharacterObj.Part.Num(); i++)
	{
		if (this->CharacterObj.Part[i].Id == Id)
		{
			return &CharacterObj.Part[i];
		}
	}
	return nullptr;
}

int32 AAwCharacter::GetChaPartIndexById(FString Id)
{
	for (int i = 0; i < this->CharacterObj.Part.Num(); i++)
	{
		if (this->CharacterObj.Part[i].Id == Id)
		{
			return i;
		}
	}
	return -1;
};

TArray<FChaPart*> AAwCharacter::GetChaPartsByType(EChaPartType PartType)
{
	TArray<FChaPart*> Res;
	for (int i = 0; i < this->CharacterObj.Part.Num(); i++)
	{
		if (this->CharacterObj.Part[i].PartType == PartType)
		{
			Res.Add(&CharacterObj.Part[i]);
		}
	}
	return Res;
}

bool AAwCharacter::HasPart(const FChaPart* CheckPart)
{
	for (int i = 0; i < CharacterObj.Part.Num(); i++)
	{
		if (CheckPart == &CharacterObj.Part[i])
		{
			return true;
		}
	}
	return false;
}

FActionInfo* AAwCharacter::CurrentAction() const
{
	return ActionComponent->CurrAction();
}

FRougeAbilityLevelInfo AAwCharacter::GetCurrentActionAbilityLevelInfo() const
{
	if (!ActionComponent)
	{
		return FRougeAbilityLevelInfo();
	}
	return ActionComponent->GetCurrentActionAbilityLevelInfo();
}

bool AAwCharacter::IsEnemy(AAwCharacter* CheckTarget)
{
	return UGameplayFuncLib::GetAwGameState()->IsEnemy(this, CheckTarget);
}

bool AAwCharacter::CanBeInteract(AAwCharacter* WhoTalkToMe)
{
	//敌对关系，或者和空气，无法交流
	if (!WhoTalkToMe || this->IsEnemy(WhoTalkToMe)) return false;
	if (this->InWar() || WhoTalkToMe->InWar()) return false;
	FString DialogModelId = this->NpcInfo.Personality.DialogModelId;
	if (this->NpcInfo.Personality.DialogAutoPicker.IsEmpty() == false)
	{
		const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(this->NpcInfo.Personality.DialogAutoPicker);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
		if (Func)
		{
			struct
			{
				FNpcInfo NpcInfo;
				TArray<FString> Params;
				FString Result;
			} FuncParam;
			FuncParam.NpcInfo = this->NpcInfo;
			FuncParam.Params = JsonFunc.Params;
			this->ProcessEvent(Func, &FuncParam);
			DialogModelId = this->NpcInfo.Personality.DialogModelId = FuncParam.Result;
		}
	}
	const FDialogScriptModel Dialog = UGameplayFuncLib::GetAwDataManager()->GetDialogById(DialogModelId);
	return Dialog.IsValidDialog();
}

bool AAwCharacter::CanHitTargetNow(AActor* TargetActor)
{
	if (TargetActor == nullptr) return false;

	const AAwCharacter* TargetCha = Cast<AAwCharacter>(TargetActor);
	if (TargetCha != nullptr && (TargetCha->Dead() || TargetCha == this)) return false;

	for (const FOffenseInfo Attack : this->CurrentActionHitInfo)
	{
		if (GetAttackHitComponent()->TargetCanBeHitByHitRecord(TargetActor, Attack.SourceId, Attack.Index) == false)
		{
			return false;
		}
	}
	return true;
}

/**
 *获取可以Cancel的动作
 *@param PrevActions 需要检查的动作们
 *@param LearntOnes 是否是只要已经学会的，false就会返回包含不会的
 *@return 可以Cancel PrevActions中至少一个的Action组成的数组
 */
TArray<FActionInfo*> AAwCharacter::GetCancellableActions(TArray<FActionInfo*> PrevActions, bool LearntOnes) const
{
	if (!ActionComponent) return TArray<FActionInfo*>();
	return ActionComponent->GetCancellableActions(PrevActions);
}

TArray<FActionInfo*> AAwCharacter::GetCancellableActions(TArray<FString> PrevActions, bool LearntOnes) const
{
	if (!ActionComponent) return TArray<FActionInfo*>();
	return ActionComponent->GetCancellableActions(PrevActions);
}

TArray<FActionInfo*> AAwCharacter::GetCurrentCancellableActions() const
{
	if (!ActionComponent) return TArray<FActionInfo*>();
	return ActionComponent->CurrentCanCancelledActions();
}

float AAwCharacter::GetRootMotionRate()
{
	int Rate = 1;
	for (const TTuple<FString, float> EachRate : RootMotionRate)
		Rate *= EachRate.Value;

	return Rate;
}

bool AAwCharacter::AttachOnTarget(UAttachPoint* MyNode, UAttachPoint* TargetNode)
{
	AAwCharacter* TargetGuy = Cast<AAwCharacter>(TargetNode->GetAttachmentRootActor());
	if (!TargetGuy || TargetGuy->Dead(true) || this->Dead(true)) return false; //没有目标或者目标死亡，无法抓
	if (TargetGuy->CanBeAttached(TargetNode) == false) return false;

	CharacterAttachment.AttachTarget = TargetNode;
	CharacterAttachment.MyPointCatchesTarget = MyNode;
	ClearAttachTargetMovementTrack();
	AddAttachTargetMovementTrack(FVector(TargetNode->GetComponentLocation()));

	this->AttachControlState = TargetNode->CatcherControlState; //因为抓住了抓点，所以我的状态变化了
	AttrRecheck();

	CharacterAttachment.HasTouched = false;

	//Attach之后我的动作要变化
	const FString ChangeToActionId = TargetNode->CatcherActionId;
	if (HasAction(ChangeToActionId))
		PreorderAction(ChangeToActionId);

	//抓住对方，也会为对方增加一个被抓住的信息
	TargetGuy->BeAttachOn(MyNode, TargetNode);

	return true;
}

void AAwCharacter::OnAttachOnTargetSuccess() const
{
	PreorderAction(CharacterAttachment.AttachTarget->CatcherActionId);
	if (CharacterAttachment.AttachTarget->CatcherTakeControl == true && this->IsPlayerCharacter() == true)
	{
		AAwCharacter* TargetGuy = Cast<AAwCharacter>(CharacterAttachment.AttachTarget->GetAttachmentRootActor());
		if (TargetGuy && CharacterAttachment.AttachTarget->CatcherTakeControl == true)
		{
			TargetGuy->SetPlayerControllable(OwnerPlayerController, true);
		}
	}
}

void AAwCharacter::BeAttachOn(UAttachPoint* TargetNode, UAttachPoint* MyNode)
{
	if (this->SeatAttachPoints.Contains(MyNode->Id))
	{
		this->CharacterAttachment.AddAttachLink(TargetNode, MyNode);
	}
}

UAttachPoint* AAwCharacter::GetAttachPointByName(FString AttachPointName, bool isCatcher)
{
	if (isCatcher == true)
	{
		return CatchAttachPoints.Contains(AttachPointName) ? this->CatchAttachPoints[AttachPointName] : nullptr;
	}
	else
	{
		return SeatAttachPoints.Contains(AttachPointName) ? this->SeatAttachPoints[AttachPointName] : nullptr;
	}
}

bool AAwCharacter::HasBeenAttachedOnAnySeat() const
{
	return this->CharacterAttachment.AttachOnMe.Num() > 0;
}

bool AAwCharacter::CanBeAttached(UAttachPoint* Point)
{
	if (Point == nullptr || this->SeatAttachPoints.Contains(Point->Id) == false) return false;
	return this->CharacterAttachment.CanBeAttached(true) &&
		this->CharacterAttachment.AttachPointHasBeenAttached(Point) == false;
}

TArray<UAttachPoint*> AAwCharacter::GetAttachPointsByChaPartId(FString PartId)
{
	TArray<UAttachPoint*> Res;
	const FChaPart* Par = this->GetChaPartById(PartId);
	if (Par)
	{
		TArray<FString> CheckIds;
		for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBox : this->CharacterHitBoxes)
		{
			if (HitBox.Get<1>() && HitBox.Get<1>()->BelongsToPart == Par)
			{
				CheckIds.Add(HitBox.Get<1>()->SeatPointId);
			}
		}
		//TODO 即将干掉
		// for (const UCharacterHitBox* HitBox : this->CharacterHitBoxes)
		// {
		// 	if (HitBox->BelongsToPart == Par)
		// 	{
		// 		CheckIds.Add(HitBox->SeatPointId);
		// 	}
		// }
		if (CheckIds.Num())
		{
			for (TTuple<FString, UAttachPoint*> PointInfo : this->SeatAttachPoints)
			{
				if (CheckIds.Contains(PointInfo.Key) == true)
				{
					Res.Add(PointInfo.Value);
				}
			}
		}
	}
	return Res;
}

void AAwCharacter::StopAttaching()
{
	AttachControlState.CanMove = EControlStateType::Normal;
	AttachControlState.CanRotate = EControlStateType::Normal;
	AttachControlState.CanJump = EControlStateType::Normal;
	AttachControlState.CanChangeAction = EControlStateType::Normal;
	AttrRecheck();

	if (CharacterAttachment.AttachTarget)
	{
		AAwCharacter* TargetGuy = Cast<AAwCharacter>(CharacterAttachment.AttachTarget->GetOwner());

		int TIndex = 0;
		while (TIndex < TargetGuy->CharacterAttachment.AttachOnMe.Num())
		{
			if (TargetGuy->CharacterAttachment.AttachOnMe[TIndex].Catcher == this)
			{
				TargetGuy->CharacterAttachment.AttachOnMe.RemoveAt(TIndex);
			}
			else
			{
				TIndex++;
			}
		}

		this->SetActorRotation(FVector(0.f, this->GetActorRotation().Yaw, 0.f).Rotation());

		if (TargetGuy && CharacterAttachment.AttachTarget->CatcherTakeControl == true)
		{
			TargetGuy->ClearPlayerInput();
			TargetGuy->SetPlayerControllable(OwnerPlayerController, false);
		}
	}

	CharacterAttachment.HasTouched = false;

	CharacterAttachment.AttachTarget = nullptr;
	CharacterAttachment.MyPointCatchesTarget = nullptr;
	this->SetReplicateMovement(true);
}

void AAwCharacter::SetPlayerControllable_Implementation(AAwPlayerController* PlayerController, bool Enable)
{
	OwnerPlayerController = PlayerController;
	if (!CmdComponent) return;
	AAwPlayerController* PC;
	if (PlayerController != nullptr)
		PC = PlayerController;
	else
		PC = UGameplayFuncLib::GetWorkingAwPlayerController();
	if (Enable)
	{
		if (!PC->ControlledCmdComps.Contains(CmdComponent))
			PC->ControlledCmdComps.Add(CmdComponent);
	}
	else
	{
		if (PC->ControlledCmdComps.Contains(CmdComponent))
		{
			PC->ControlledCmdComps.Remove(CmdComponent);
		}
	}

	if (Enable)
		OwnerPlayerController->InitInClient(this);
}

TArray<UMeshComponent*> AAwCharacter::GetAllMeshComponents()
{
	TArray<UMeshComponent*> MeshComps;

	MeshComps.Add(GetMesh());

	for (const TTuple<FString, UMeshComponent*> BodyPartHere : BodySightParts)
		MeshComps.Add(BodyPartHere.Value);

	for (const FWearingAppearanceInfo AppInfo : this->WearingAppearance)
	{
		if (AppInfo.Actor)
		{
			TArray<UMeshComponent*> AppComps;
			AppInfo.Actor->GetComponents<UMeshComponent>(AppComps, true);
			for (UMeshComponent* AppComp : AppComps)
				if (AppComp) MeshComps.Add(AppComp);
		}
	}

	return MeshComps;
}

USqueezeComp* AAwCharacter::GetSqueezeComp() const
{
	UActorComponent* Actor = GetComponentByClass(USqueezeComp::StaticClass());
	if (IsValid(Actor))
		return Cast<USqueezeComp>(Actor);

	return nullptr;
}

UInterruptComponent* AAwCharacter::GetInterruptComp() const
{
	UActorComponent* Actor = GetComponentByClass(UInterruptComponent::StaticClass());
	if (IsValid(Actor))
		return Cast<UInterruptComponent>(Actor);

	return nullptr;
}

bool AAwCharacter::UnderPlayerControl() const
{
	// return bUnderPlayerControl;
	if (!IsValid(CmdComponent)) return false;
	const AAwPlayerController* PC = OwnerPlayerController;
	if (!PC) return false;
	return PC->ControlledCmdComps.Contains(CmdComponent);
}

bool AAwCharacter::CostMana(float Value, int Below)
{
	this->CharacterObj.CurrentRes.MP -= Value;
	//this->SPMax += FMath::Min(0.f, -Value / 2.f);	
	this->CharacterObj.CurrentRes.MP = FMath::Clamp(this->CharacterObj.CurrentRes.MP, 0,
	                                                this->CharacterObj.CurProperty.MP); // this->SPMax);
	return CharacterObj.CurrentRes.MP <= Below;
}

void AAwCharacter::SwitchActionForceRotateOnce(FString ActionId, uint8 Index, bool Open, float DegreeLimit)
{
	if (AllowForceRotate.Contains(ActionId))
	{
		if (Open == true)
		{
			bool Found = false;
			for (int i = 0; i < AllowForceRotate[ActionId].Num(); i++)
			{
				if (AllowForceRotate[ActionId][i].Index == Index)
				{
					Found = true;
					break;
				}
			}
			if (Found == false)
				AllowForceRotate[ActionId].Add(
					FActionForceRotateInfo::Create(ActionId, Index, DegreeLimit));
			//后加的同一个Index的角度会无效化，但这也算不得是bug，是策划填表问题
		}
		else
		{
			int TIndex = 0;
			while (TIndex < AllowForceRotate[ActionId].Num())
			{
				if (AllowForceRotate[ActionId][TIndex].Index == Index)
				{
					AllowForceRotate[ActionId].RemoveAt(TIndex);
				}
				else
				{
					TIndex++;
				}
			}
			if (AllowForceRotate[ActionId].Num() <= 0)
				AllowForceRotate.Remove(ActionId);
		}
	}
	else
	{
		if (Open == true)
		{
			TArray<FActionForceRotateInfo> Indexes;
			Indexes.Add(FActionForceRotateInfo::Create(ActionId, Index, DegreeLimit));
			AllowForceRotate.Add(ActionId, Indexes);
		}
	}
}

bool AAwCharacter::InWar() const
{
	return this->InWarDuration > 0;
}

FActionForceRotateInfo* AAwCharacter::CurActionForceRotate()
{
	const FActionInfo* CurAction = CurrentAction();
	if (CurAction == nullptr) return nullptr;
	if (CurAction->Id.IsEmpty()) return nullptr;
	const FString ActionId = CurAction->Id;
	if (AllowForceRotate.Contains(ActionId) == false)
		return nullptr;
	else if (AllowForceRotate[ActionId].Num() <= 0)
		return nullptr;
	//返回一个最大的
	FActionForceRotateInfo* Res = &AllowForceRotate[ActionId][0];
	for (int i = 0; i < AllowForceRotate[ActionId].Num(); i++)
	{
		if (AllowForceRotate[ActionId][i].DegreeLimit > Res->DegreeLimit)
		{
			Res = &AllowForceRotate[ActionId][i];
		}
	}
	return Res;
}

int AAwCharacter::GetEquipmentIndexByChaPart(EChaPartType ChaPart)
{
	for (int i = 0; i < CharacterObj.Equipments.Num(); i++)
	{
		if (CharacterObj.Equipments[i].AffectPart == ChaPart)
		{
			return i;
		}
	}
	return -1;
}

int AAwCharacter::GetEquipmentIndexByPart(EEquipmentPart EquipPart)
{
	if (EquipPart == EEquipmentPart::Invalid) return -1;
	for (int i = 0; i < CharacterObj.Equipments.Num(); i++)
	{
		if (CharacterObj.Equipments[i].PartType == EquipPart)
		{
			return i;
		}
	}
	return -1;
}

void AAwCharacter::RefreshEquipmentAppearance()
{
	//外观
	TArray<FEquipmentAppearancePart> ToShowEquipment = EquipmentsRequireShowAppearance();

	//删除所有已经不一样的
	int i = 0;
	while (i < this->WearingAppearance.Num())
	{
		bool FoundToShow = false;
		int j = 0;
		while (j < ToShowEquipment.Num())
		{
			if (ToShowEquipment[j].PartSlot == WearingAppearance[i].Slot)
			{
				//发现同slot的，观察是否一致，不一致就要创建新的干掉老的
				if (ToShowEquipment[j].BluePrintPath != WearingAppearance[i].BPPath)
				{
					if (WearingAppearance[i].Actor) WearingAppearance[i].Actor->Destroy();
					WearingAppearance[i] =
						FWearingAppearanceInfo::FromEquipmentAppearance(
							ShowAppearance(ToShowEquipment[j]),
							&ToShowEquipment[j],
							ToShowEquipment[j].PartType
						);
					//FWearingAppearanceInfo(ToShowEquipment[j].BluePrintPath, ShowAppearance(ToShowEquipment[j]));
				}
				else
				{
					if (WearingAppearance[i].Actor)
					{
						const int ArmIndex = static_cast<int32>(GetArmState());
						if (ArmIndex >= 0 && ArmIndex < ToShowEquipment[j].BindPointIds.Num())
						{
							if (EquipmentBindPoints.Contains(ToShowEquipment[j].BindPointIds[ArmIndex]))
							{
								WearingAppearance[i].Actor->AttachToComponent(
									EquipmentBindPoints[ToShowEquipment[j].BindPointIds[ArmIndex]],
									FAttachmentTransformRules::KeepRelativeTransform
								);
								WearingAppearance[i].Actor->SetActorRelativeTransform(FTransform::Identity);
							}
						}
					}
				}
				ToShowEquipment.RemoveAt(j);
				FoundToShow = true;
				break;
			}
			else
			{
				//不存在
				j++;
			}
		}
		if (FoundToShow == true)
		{
			i++;
		}
		else
		{
			if (WearingAppearance[i].Actor) WearingAppearance[i].Actor->Destroy();
			WearingAppearance.RemoveAt(i);
		}
	}
	//显示还未现实的
	for (FEquipmentAppearancePart ToShow : ToShowEquipment)
	{
		WearingAppearance.Add(
			FWearingAppearanceInfo::FromEquipmentAppearance(
				ShowAppearance(ToShow),
				&ToShow,
				ToShow.PartType
			));
	}
	//检查尸块隐藏问题
	RecheckBodySightPartsVisible();
}

bool AAwCharacter::CheckWearingEquipActorBindPoint(const AActor* Actor, const FString BindPoint)
{
	if (Actor && Actor->GetParentComponent() && BindPoint != "")
		return Actor->GetParentComponent()->GetName().Contains(BindPoint);

	return false;
}

void AAwCharacter::WearEquipment(FEquipment ToEquip)
{
	const int WasEquipmentIndex = GetEquipmentIndexByPart(ToEquip.PartType);
	//联机判断,只在本地执行修改背包
	if (UGameplayFuncLib::GetAllLocalAwPlayerControllers().Contains(OwnerPlayerController))
	{
		int ToEquipmentInRole = -1;
		for (int i = 0; i < UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs.Num(); i++)
		{
			if (UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i] == ToEquip)
			{
				ToEquipmentInRole = i;
				break;
			}
		}
		if (ToEquipmentInRole >= 0)
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs.RemoveAt(
				ToEquipmentInRole);
		if (WasEquipmentIndex >= 0)
		{
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs.Add(
				CharacterObj.Equipments[WasEquipmentIndex]);
			CharacterObj.Equipments.RemoveAt(WasEquipmentIndex);
		}
	}

	CharacterObj.Equipments.Add(ToEquip);

	//重新计算属性
	this->CharacterObj.EquipmentProp.ResetToZero();
	for (FEquipment& TheEquipment : this->CharacterObj.Equipments)
	{
		this->CharacterObj.EquipmentProp = this->CharacterObj.EquipmentProp + TheEquipment.Property;
		TArray<FChaPart*> AffectParts = GetChaPartsByType(TheEquipment.AffectPart);
		for (FChaPart* ThePart : AffectParts)
		{
			ThePart->Meat = TheEquipment.Meat;
			ThePart->MeatType = TheEquipment.MeatType;
		}
	}
	AttrRecheck();

	//外观
	RefreshEquipmentAppearance();
}

void AAwCharacter::WearEquipmentById(FString EquipmentId)
{
	if (EquipmentId.IsEmpty()) return;
	FEquipment Equipment = UGameplayFuncLib::GetDataManager()->GetEquipmentById(EquipmentId);
	FString EquipmentPart = UDataFuncLib::EnumToFString(Equipment.PartType);
	Equipment.UniqueId = UEnum::GetValueAsString(Equipment.PartType)
		.Append(FDateTime::Now().ToString()
		                        .Append("_")
		                        .Append(FString::FromInt(FMath::RandRange(0, 999999))));
	this->WearEquipment(Equipment);
}

TMap<ESlotInWeaponObj, FString> AAwCharacter::WearWeapon(FEquippedWeaponSet ToEquip)
{
	const FEquippedWeaponSet OldWeapon = this->CharacterObj.WeaponSet;
	TMap<ESlotInWeaponObj, FString> Res;
	//老的放回包里 
	if (OldWeapon.IsNull() == false)
	{
		if (OldWeapon.MainHand.IsNull() == false && OldWeapon.MainHand.IsTemporary == false)
		{
			if (UGameplayFuncLib::GetAllLocalAwPlayerControllers().Contains(OwnerPlayerController)) //联机判断,只在本地执行修改背包
				UAwGameInstance::Instance->RoleInfo.WeaponObjs.Add(OldWeapon.MainHand);
			Res.Add(ESlotInWeaponObj::MainWeapon, OldWeapon.MainHand.UniqueId);
		}
		if (OldWeapon.OffHand.IsNull() == false && OldWeapon.OffHand.IsTemporary == false
		)
		{
			if (UGameplayFuncLib::GetAllLocalAwPlayerControllers().Contains(OwnerPlayerController)) //联机判断,只在本地执行修改背包
				UAwGameInstance::Instance->RoleInfo.WeaponObjs.Add(OldWeapon.OffHand);
			Res.Add(ESlotInWeaponObj::OffWeapon, OldWeapon.OffHand.UniqueId);
		}
	}

	//新的
	this->CharacterObj.WeaponSet = ToEquip;
	if (ToEquip.IsNull() == false)
	{
		if (ToEquip.MainHand.IsNull() == false)
		{
			if (UGameplayFuncLib::GetAllLocalAwPlayerControllers().Contains(OwnerPlayerController)) //联机判断,只在本地执行修改背包
				UAwGameInstance::Instance->RoleInfo.WeaponObjs.Remove(ToEquip.MainHand);
		}
		if (ToEquip.OffHand.IsNull() == false)
		{
			if (UGameplayFuncLib::GetAllLocalAwPlayerControllers().Contains(OwnerPlayerController)) //联机判断,只在本地执行修改背包
				UAwGameInstance::Instance->RoleInfo.WeaponObjs.Remove(ToEquip.OffHand);
		}
	}
	//重新计算属性
	this->CharacterObj.WeaponProp =
		ToEquip.MainHand.Model.Property +
		ToEquip.OffHand.Model.Property;
	AttrRecheck();

	//外观
	RefreshEquipmentAppearance();

	return Res;
}

TArray<FEquipment> AAwCharacter::UnwearEquipment(EChaPartType OnPart)
{
	TArray<FEquipment> Res;
	int i = 0;
	while (i < CharacterObj.Equipments.Num())
	{
		if (CharacterObj.Equipments[i].AffectPart == OnPart)
		{
			Res.Add(CharacterObj.Equipments[i]);
			CharacterObj.Equipments.RemoveAt(i);
		}
		else
		{
			i++;
		}
	}

	//重新计算属性
	this->CharacterObj.EquipmentProp.ResetToZero();
	for (FEquipment& TheEquipment : this->CharacterObj.Equipments)
	{
		this->CharacterObj.EquipmentProp = this->CharacterObj.EquipmentProp + TheEquipment.Property;
		TArray<FChaPart*> AffectParts = GetChaPartsByType(TheEquipment.AffectPart);
		for (FChaPart* ThePart : AffectParts)
		{
			ThePart->Meat = TheEquipment.Meat;
			ThePart->MeatType = TheEquipment.MeatType;
		}
	}
	AttrRecheck();

	//外观
	RefreshEquipmentAppearance();

	return Res;
}

FEquippedWeaponSet AAwCharacter::UnwearWeapon()
{
	FEquippedWeaponSet Res = this->CharacterObj.WeaponSet;

	//重新计算属性
	this->CharacterObj.WeaponProp.ResetToZero();
	AttrRecheck();

	//外观
	RefreshEquipmentAppearance();

	return Res;
}


TArray<FEquipmentAppearancePart> AAwCharacter::EquipmentsRequireShowAppearance()
{
	TMap<FString, FEquipmentAppearancePart> Res;
	for (const FEquipment ThisEquipment : this->CharacterObj.Equipments)
	{
		for (FEquipmentAppearancePart AppPart : ThisEquipment.AppearanceParts)
		{
			//不符合显示标准的continue掉
			if (AppPart.ShowOnCharacterType.Num() > 0 && AppPart.ShowOnCharacterType.Contains(CharacterObj.TypeId) ==
				false)
				continue;
			if (AppPart.ShowAboveDurability > ThisEquipment.Durability || AppPart.ShowBelowDurability < ThisEquipment.
				Durability)
				continue;

			bool ToAdd = true;
			for (FString ConflictSlot : AppPart.ConflictPartSlots)
			{
				if (Res.Contains(ConflictSlot) && Res[ConflictSlot].Priority > AppPart.Priority)
				{
					ToAdd = false;
					break;
				}
			}
			if (ToAdd == true)
			{
				//先删除冲突的部位
				for (FString ConflictSlot : AppPart.ConflictPartSlots)
				{
					Res.Remove(ConflictSlot);
				}
				Res.Add(AppPart.PartSlot, AppPart);
			}
		}
	}
	CharacterObj.WeaponSet.RecheckShowParts(this->CharacterObj.TypeId); //这里已经过滤了不该显示的了
	for (FEquipmentAppearancePart AppPart : CharacterObj.WeaponSet.ShowPartNow)
	{
		bool ToAdd = true;
		for (FString ConflictSlot : AppPart.ConflictPartSlots)
		{
			if (Res.Contains(ConflictSlot) && Res[ConflictSlot].Priority > AppPart.Priority)
			{
				ToAdd = false;
				break;
			}
		}
		if (ToAdd == true)
		{
			//先删除冲突的部位
			for (FString ConflictSlot : AppPart.ConflictPartSlots)
			{
				Res.Remove(ConflictSlot);
			}
			Res.Add(AppPart.PartSlot, AppPart);
		}
	}
	TArray<FEquipmentAppearancePart> Result;
	for (TTuple<FString, FEquipmentAppearancePart> Re : Res)
	{
		Result.Add(Re.Value);
	}
	return Result;
}

void AAwCharacter::AddTempItemInHand(FEquipmentAppearancePart ItemApp)
{
	const FString BindPointId = ItemApp.BindPointIds[static_cast<int32>(GetArmState())];
	if (EquipmentBindPoints.Contains(BindPointId))
	{
		UEquipmentBindPoint* BiPoint = EquipmentBindPoints[BindPointId];
		AActor* App = UGameplayFuncLib::CreateActorByBP(ItemApp.BluePrintPath, FTransform());
		if (App)
		{
			App->AttachToComponent(BiPoint, FAttachmentTransformRules::SnapToTargetIncludingScale);
			if (ItemApp.Type == EEquipmentAppearanceType::SkinnedMesh)
			{
				UActorComponent* Comp = App->GetComponentByClass(USkeletalMeshComponent::StaticClass());
				if (Comp)
				{
					USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
					if (MeshComp)
					{
						MeshComp->SetLeaderPoseComponent(this->GetMesh(), true);
					}
				}
			}
			else if (ItemApp.Type == EEquipmentAppearanceType::Physical)
			{
				UActorComponent* Comp = App->GetComponentByClass(USkeletalMeshComponent::StaticClass());
				if (Comp)
				{
					USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
					for (FString PhysicalBoneName : ItemApp.PhysicalBoneName)
						MeshComp->SetAllBodiesBelowSimulatePhysics(FName(*PhysicalBoneName), true, true);
				}
			}
			this->ItemsInHand.Add(App);
		}
	}
}

void AAwCharacter::RemoveAllItemsInHand()
{
	for (AActor* Item : this->ItemsInHand)
	{
		Item->Destroy();
	}
	this->ItemsInHand.Empty();
}

void AAwCharacter::HideAllAppearance()
{
	for (const FWearingAppearanceInfo App : this->WearingAppearance)
	{
		if (App.Actor) App.Actor->Destroy();
	}
	this->WearingAppearance.Empty();
}

void AAwCharacter::SetAppearanceVisible(bool ToVisible)
{
	for (const FWearingAppearanceInfo App : this->WearingAppearance)
	{
		if (App.Actor) App.Actor->SetActorHiddenInGame(!ToVisible);
	}
}

AActor* AAwCharacter::ShowAppearance(FEquipmentAppearancePart AppearanceEquipment)
{
	if (AppearanceEquipment.BluePrintPath.IsEmpty()) return nullptr;

	const FString BindPointId = AppearanceEquipment.BindPointIds[static_cast<int32>(GetArmState())];
	if (!EquipmentBindPoints.Contains(BindPointId)) return nullptr;
	UEquipmentBindPoint* BiPoint = EquipmentBindPoints[BindPointId];
	if (!BiPoint) return nullptr;

	AActor* Res = UGameplayFuncLib::CreateActorByBP(AppearanceEquipment.BluePrintPath, FTransform());
	if (Res)
	{
		Res->AttachToComponent(BiPoint, FAttachmentTransformRules::KeepRelativeTransform);
		if (AppearanceEquipment.Type == EEquipmentAppearanceType::SkinnedMesh)
		{
			UActorComponent* Comp = Res->GetComponentByClass(USkeletalMeshComponent::StaticClass());
			if (Comp)
			{
				USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
				if (MeshComp)
				{
					MeshComp->SetLeaderPoseComponent(this->GetMesh(), true);
				}
			}
		}
		else if (AppearanceEquipment.Type == EEquipmentAppearanceType::Physical)
		{
			UActorComponent* Comp = Res->GetComponentByClass(USkeletalMeshComponent::StaticClass());
			if (Comp)
			{
				USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
				for (FString PhysicalBoneName : AppearanceEquipment.PhysicalBoneName)
					MeshComp->SetAllBodiesBelowSimulatePhysics(FName(*PhysicalBoneName), true, true);
			}
		}
	}
	return Res;
}

void AAwCharacter::RecheckBodySightPartsVisible()
{
	TArray<FString> HideParts;
	for (const FWearingAppearanceInfo App : this->WearingAppearance)
	{
		for (FString ThisPart : App.HideBodyParts)
		{
			if (HideParts.Contains(ThisPart) == false) HideParts.Add(ThisPart);
		}
	}

	for (const TTuple<FString, UMeshComponent*>& BSPart : this->BodySightParts)
	{
		if (BSPart.Value)
			BSPart.Value->SetVisibility(!HideParts.Contains(BSPart.Key));
	}
}

EClassWeaponType AAwCharacter::CurrentWeaponType() const
{
	return CharacterObj.WeaponSet.WeaponType;
}

FString AAwCharacter::EquipmentFXKey(EEquipmentPart OnPart, int FXIndex)
{
	const int EIndex = GetEquipmentIndexByPart(OnPart);
	if (EIndex < 0 || FXIndex < 0 || CharacterObj.Equipments[EIndex].FXKey.Num() <= FXIndex) return FString();
	return CharacterObj.Equipments[EIndex].FXKey[FXIndex];
}

void AAwCharacter::RevivedOnSecondWind()
{
	//this->HPMax = FMath::CeilToInt(this->CurProperty.HP * 0.2f);
	this->CharacterObj.CurrentRes.HP = FMath::CeilToInt(this->CharacterObj.CurProperty.HP * 0.2f);
	PreorderActionByMontageState(ECharacterMontageState::GetUp, FActionParam(), 0);
	this->SecondWind = this->PlayerClassId.IsEmpty() == false ? 2.000f : 0;
}

USceneComponent* AAwCharacter::MostPriorityHitBox()
{
	USceneComponent* ResItem = nullptr;
	const UCharacterHitBoxData* Res = nullptr;
	for (TTuple<USceneComponent*, UCharacterHitBoxData*> HitBox : this->CharacterHitBoxes)
	{
		if (!Res || !Res->BelongsToPart || Res->Priority < (HitBox.Get<1>() && HitBox.Get<1>()->BelongsToPart
			                                                    ? HitBox.Get<1>()->Priority
			                                                    : -1))
		{
			ResItem = HitBox.Get<0>();
			Res = HitBox.Get<1>();
		}
	}
	return ResItem;
}

// UCharacterHitBox* AAwCharacter::MostPriorityHitBox()
// {
// 	UCharacterHitBox* Res = nullptr;
// 	for (UCharacterHitBox* HitBox : this->CharacterHitBoxes)
// 	{
// 		if (!Res || !Res->BelongsToPart || Res->Priority < (HitBox->BelongsToPart ? HitBox->Priority : -1))
// 		{
// 			Res = HitBox;
// 		}
// 	}
// 	return Res;
// }

FOffenseInfo AAwCharacter::GetOffenseInfoByAttackHitBox(UAttackHitBox* AttackHitBox)
{
	const FString FindHitBoxName = AttackHitBox->GetName();
	for (FOffenseInfo AInfo : this->CurrentActionHitInfo)
	{
		for (FString HitBoxName : AInfo.AttackHitBoxName)
		{
			if (HitBoxName == FindHitBoxName)
				return AInfo;
		}
	}
	return FOffenseInfo();
}

FChaPart* AAwCharacter::MostPriorityPart()
{
	FChaPart* Res = nullptr;
	for (FChaPart ThisPart : this->CharacterObj.Part)
	{
		if (!Res)
		{
			Res = &ThisPart;
		}
	}
	return Res;
}

TArray<FEquipment> AAwCharacter::EquippedEquipments() const
{
	return CharacterObj.Equipments;
}

FEquipment* AAwCharacter::GetEquipmentAtIndex(int Index)
{
	if (Index < 0 || Index >= CharacterObj.Equipments.Num()) return nullptr;
	return &CharacterObj.Equipments[Index];
}

void AAwCharacter::AddActionHitInfo(FOffenseInfo ToAdd)
{
	// for (const FOffenseInfo AInfo : this->CurrentActionHitInfo)
	// {
	// 	if (AInfo.SourceId == ToAdd.SourceId && AInfo.Index == ToAdd.Index)
	// 	{
	// 		return;
	// 	}
	// }

	CurrentActionHitInfo.Add(ToAdd);
}

void AAwCharacter::RemoveActionHitInfo(FOffenseInfo ToRemove)
{
	int i = 0;
	while (i < CurrentActionHitInfo.Num())
	{
		if (CurrentActionHitInfo[i].Index == ToRemove.Index && CurrentActionHitInfo[i].SourceId == ToRemove.SourceId)
		{
			CurrentActionHitInfo.RemoveAt(i);
		}
		else
		{
			i++;
		}
	}
}

bool AAwCharacter::CheckHasAttackSourceInCurActionHitInfo(EAttackSource AttackSource)
{
	bool Res = false;
	for (const FOffenseInfo Info : CurrentActionHitInfo)
	{
		if (Info.AttackInfo.DamageSourceType == AttackSource)
		{
			Res = true;
			break;
		}
	}
	return Res;
}

void AAwCharacter::Setup(FMobModel NewMobModel) const
{
	if (ActionComponent)
	{
		ActionComponent->SetUp(NewMobModel);
		//ActionComponent->AddActions(UGameplayFuncLib::GetDataManager()->GetBaseActionsById(MobModel.BaseActionType));
	}
	if (AIComponent)
	{
		AIComponent->SetUp(NewMobModel);
	}
}

void AAwCharacter::Setup(FBattleClassModel NewClassInfo, FString TypeId) const
{
	if (ActionComponent)
	{
		ActionComponent->SetUp(NewClassInfo, TypeId);
		//ActionComponent->AddActions(UGameplayFuncLib::GetDataManager()->GetBaseActionsById(ClassInfo.BaseActionType));
	}
	if (MoveComponent)
	{
		MoveComponent->FallWeight = NewClassInfo.FallWeight;
	}
}

FControlState* AAwCharacter::GetActionControlState() const
{
	return ActionComponent ? &ActionComponent->ActionControlState : nullptr;
}

void AAwCharacter::PreorderActionByMontageState(ECharacterMontageState MontageState, FActionParam Params,
                                                float FromSec) const
{
	if (ActionComponent)
	{
		ActionComponent->PreorderActionByMontageState(MontageState, Params, FromSec);
	}
}

void AAwCharacter::PreorderActionByActionChangeInfo(FActionChangeInfo Info, FActionParam ActionParam) const
{
	if (!ActionComponent) return;

	switch (Info.ChangeMethod)
	{
	case EActionChangeMethod::Keep:
		SlowDownActionOnHit(Info);
		return;
	case EActionChangeMethod::ChangeActionInfo:
	case EActionChangeMethod::ToMontageState:
		PreorderAction(Info, ActionParam);
		return;
	default: return;
	}
}

void AAwCharacter::PreorderAction(FActionChangeInfo Info, FActionParam ActionParam) const
{
	if (ActionComponent)
	{
		ActionComponent->PreorderAction(Info, ActionParam);
	}
}

void AAwCharacter::TryPreorderAction(FString ActionId, FActionParam Params, float FromSec) const
{
	if (ActionComponent)
	{
		ActionComponent->TryPreorderActionById(ActionId, Params, FromSec);
	}
}

void AAwCharacter::PreorderAction(FString ActionId, FActionParam Params, float FromSec) const
{
	if (ActionComponent)
	{
		ActionComponent->PreorderActionById(ActionId, Params, FromSec);
	}
}

void AAwCharacter::CheckOnGround()
{
	if (MoveComponent && MoveComponent->bStandOnGround)
	{
		FVector newPos = GetActorLocation();
		LastPosOnGround = newPos;
	}
}

void AAwCharacter::PreorderActionWithCancelCheck(FString ActionId, FActionParam Params, float FromSec) const
{
	if (ActionComponent && ActionComponent->ActionCanCancelCurAction(ActionId))
	{
		ActionComponent->PreorderActionById(ActionId, Params, FromSec);
	}
}

void AAwCharacter::SlowDownActionOnHit(FActionChangeInfo ActionInfo) const
{
	if (AwAnimInstance)
	{
		UE_LOG(LogTemp, Log, TEXT("Start Freeze for %f seconds. by %s"), ActionInfo.FreezeTime, *GetName());
		AwAnimInstance->FreezeAnim(ActionInfo.FreezeTime, ActionInfo.HitStun);
	}
}

void AAwCharacter::AddForceMove_Implementation(FForceMoveInfo Move)
{
	if (MoveComponent)
	{
		MoveComponent->AddForceMove(Move);
	}
}

void AAwCharacter::ClearAttachTargetMovementTrack_Implementation()
{
	if (MoveComponent)
	{
		MoveComponent->AttachTargetMovementTrack.Empty();
	}
}

void AAwCharacter::AddAttachTargetMovementTrack_Implementation(FVector Node)
{
	if (MoveComponent)
	{
		MoveComponent->AttachTargetMovementTrack.Add(Node);
	}
}

bool AAwCharacter::HasAction(FString ActionId, bool OnlyLearnt) const
{
	if (!ActionComponent) return false;
	return ActionComponent->HasAction(ActionId);
}

void AAwCharacter::ClearPlayerInput() const
{
	if (CmdComponent) CmdComponent->CleanPlayerInput();
}

bool AAwCharacter::AnyActionOccur(TArray<TTuple<FString, EAwInputState>> CheckInput) const
{
	if (!CmdComponent) return false;
	return CmdComponent->AnyActionOccur(CheckInput);
}

bool AAwCharacter::IsActionOccur(FString Cmd, EAwInputState InputState, bool RemoveInputs) const
{
	if (Cmd.IsEmpty()) return false;
	if (!CmdComponent) return false;
	return CmdComponent->IsActionOccur(Cmd, InputState, RemoveInputs);
}

float AAwCharacter::GetMoveSpeed() const
{
	if (!MoveComponent) return 0;
	return MoveComponent->GetMoveSpeed();
}

FActionInfo* AAwCharacter::GetActionById(FString ActionId) const
{
	if (!ActionComponent) return nullptr;
	return ActionComponent->GetActionById(ActionId);
}

FActionInfo* AAwCharacter::GetActionByMontageState(ECharacterMontageState State) const
{
	if (!ActionComponent) return nullptr;
	return ActionComponent->GetActionByMontageState(State);
}

FActionInfo* AAwCharacter::GetActionByActionState(ECharacterActionState State) const
{
	if (!ActionComponent) return nullptr;
	return ActionComponent->GetActionByActionState(State);
}

void AAwCharacter::AddCancelTag(ECancelTagType TagType, int CancelPointIndex, float Duration, FActionInfo MAction) const
{
	if (ActionComponent)
	{
		ActionComponent->AddCancelTag(TagType, CancelPointIndex, Duration, MAction);
	}
}

void AAwCharacter::RemoveAllCancelTags() const
{
	if (ActionComponent)
	{
		ActionComponent->RemoveAllCancelTags();
	}
}

void AAwCharacter::RemoveMontageCancelTag(int CancelPointIndex, FActionInfo MAction) const
{
	if (ActionComponent)
	{
		ActionComponent->RemoveMontageCancelTag(CancelPointIndex, MAction);
	}
}

void AAwCharacter::RemoveStateCancelTag(int CancelPointIndex) const
{
	if (ActionComponent)
	{
		ActionComponent->RemoveStateCancelTag(CancelPointIndex);
	}
}

void AAwCharacter::RemoveTemporaryCancelTag(int CancelPointIndex) const
{
	if (ActionComponent)
	{
		ActionComponent->RemoveTemporaryCancelTag(CancelPointIndex);
	}
}

ECharacterActionState AAwCharacter::CurrentActionState() const
{
	if (ActionComponent)
	{
		return ActionComponent->CurrentActionState();
	}
	return ECharacterActionState::Ground;
}

void AAwCharacter::StopCurrentAction(bool Forced) const
{
	if (ActionComponent) ActionComponent->StopCurrentAction(Forced);
}

FAnimMontageInstance* AAwCharacter::GetActiveMontageInstance() const
{
	if (!AwAnimInstance) return nullptr;
	return AwAnimInstance->GetActiveMontageInstance();
}

FAnimMontageInstance* AAwCharacter::GetRootMotionMontageInstance() const
{
	if (!AwAnimInstance) return nullptr;
	return AwAnimInstance->GetRootMotionMontageInstance();
}

UAnimMontage* AAwCharacter::GetCurrentActiveMontage() const
{
	if (!AwAnimInstance) return nullptr;
	return AwAnimInstance->GetCurrentActiveMontage();
}

void AAwCharacter::SetActiveMontagePosition(float ToSec) const
{
	if (!AwAnimInstance) return;
	FAnimMontageInstance* Montage = AwAnimInstance->GetActiveMontageInstance();
	if (Montage) Montage->SetPosition(ToSec);
}

void AAwCharacter::SetActionGravityTimes(float Times) const
{
	if (MoveComponent)
	{
		MoveComponent->SetActionGravityTimes(Times);
	}
}

void AAwCharacter::AwJump(float Height, float InSec) const
{
	if (MoveComponent)
	{
		MoveComponent->Jump(Height, InSec);
	}
}

void AAwCharacter::TakeOff(float FlySpeed) const
{
	if (MoveComponent)
	{
		MoveComponent->TakeOff(FlySpeed);
	}
}

void AAwCharacter::SetInputAcceptance(FVector MoveTimes, float RotateTimes)
{
	Notify_SpeedAcceptance = MoveTimes;
	Notify_RotateAcceptance = RotateTimes;
	// UKismetSystemLibrary::PrintString(this, FString("Input Set ").Append(Notify_SpeedAcceptance.ToString()),
	// 	true, true, FLinearColor::Green, 20);
}

int AAwCharacter::GetMoveSpeedLevel() const
{
	if (!CmdComponent) return 0;
	return CmdComponent->GetSpeedLevel();
}

void AAwCharacter::SetMoveSpeedLevel(int SpeedLevel, int StateLevel) const
{
	if (!AwAnimInstance) return;
	AwAnimInstance->SetAnimForwardSpeed(SpeedLevel);
	AwAnimInstance->SetAnimSideways(StateLevel);
}

void AAwCharacter::SetForwardSpeedLevelLimit(int LimitLevel) const
{
	if (!AwAnimInstance) return;
	AwAnimInstance->SetAnimForwardSpeedLimit(LimitLevel);
}

void AAwCharacter::SetActionMoveSpeedLevelLimit(bool On, int ToLevel) const
{
	if (!ActionComponent) return;
	ActionComponent->SetToSpdLv(On, ToLevel);
}

FVector2D AAwCharacter::GetMoveDirection() const
{
	if (!CmdComponent) return FVector2D();
	return CmdComponent->GetMoveDir();
}


void AAwCharacter::SetXYMove(FVector2D Direction, int MoveSpeedLevel) const
{
	if (MoveComponent)
	{
		MoveComponent->SetXYMove(Direction, MoveSpeedLevel);
	}
}

bool AAwCharacter::PlayMontageAnim(FString MontagePath, FActionPlanInfo ActionPlanInfo) const
{
	if (AwAnimInstance)
	{
		return AwAnimInstance->PlayActionMontage(MontagePath, ActionPlanInfo);
	}
	return false;
}

float AAwCharacter::PlayMontageAnim(UAnimMontage* Montage, float PlayRate, float FromSec) const
{
	if (AwAnimInstance)
	{
		return AwAnimInstance->Montage_Play(Montage, PlayRate, EMontagePlayReturnType::MontageLength, FromSec);
	}
	return 0;
}

void AAwCharacter::PlayMoveBlendSpace(FString AnimPath) const
{
	if (AwAnimInstance)
		AwAnimInstance->PlayMoveBlendSpace(AnimPath);
}

void AAwCharacter::StopAllMontages(float BlendOut) const
{
	if (AwAnimInstance)
		AwAnimInstance->StopAllMontages(BlendOut);
}

void AAwCharacter::ClearAICmd() const
{
	if (CmdComponent)
		CmdComponent->ClearAiCmd();
}

bool AAwCharacter::InAiming() const
{
	if (!CmdComponent) return false;
	return CmdComponent->InAiming;
}

void AAwCharacter::SetCmdInAiming(bool On) const
{
	if (CmdComponent)
		CmdComponent->InAiming = On;
}

FVector2D AAwCharacter::GetRotateDir() const
{
	if (!CmdComponent) return FVector2D();
	if (CmdComponent->InAiming)
		return CmdComponent->GetRotateDir();
	else
		return CmdComponent->GetMoveDir();
}

void AAwCharacter::SetDir(float Yaw)
{
	if (MoveComponent)
		MoveComponent->SetActorDir(Yaw);
}

void AAwCharacter::AddAIAction(FString ActionId) const
{
	if (CmdComponent)
		CmdComponent->AddAIAction(ActionId);
}

void AAwCharacter::SetMoveDirection(FVector2D Direction) const
{
	if (CmdComponent)
	{
		CmdComponent->SetMoveDir(Direction);
	}
}

/**
 * 设置面朝方向
 */
void AAwCharacter::SetFaceDirection(FVector2D Direction) const
{
	if (MoveComponent)
		MoveComponent->SetForceRotate(Direction);
}

void AAwCharacter::SetAIMoveAndFaceDir(FVector2D MoveDir, FVector2D FaceDir, int SpeedLevel) const
{
	if (CmdComponent)
		CmdComponent->SetAIMoveAndFaceDir(MoveDir, FaceDir, SpeedLevel);
}

FVector2D AAwCharacter::GetFaceDir() const
{
	return FVector2D(this->GetActorRotation().Vector().X, this->GetActorRotation().Vector().Y);
}

void AAwCharacter::StopCurrentForceMove() const
{
	if (MoveComponent)
		MoveComponent->StopCurrentForceMove();
}

void AAwCharacter::SetKeepMoving(bool ToActive, EMoveDirMergeRule Rule) const
{
	if (MoveComponent)
		MoveComponent->SetKeepMovingActive(ToActive, Rule);
}

FVector AAwCharacter::GetCharacterGroundLoc()
{
	const TArray<AActor*> ActorsToIgnore;
	FHitResult HitResult;

	const float Radius = this->GetCapsuleComponent()->GetScaledCapsuleRadius();
	const FVector Start = this->GetActorLocation();
	const FVector End = Start + FVector::DownVector * (10000);

	TArray<TEnumAsByte<EObjectTypeQuery>> ObjectList;
	ObjectList.Add(ObjectTypeQuery1);
	const bool bIsHit = UKismetSystemLibrary::SphereTraceSingleForObjects(
		this, Start, End, Radius,
		ObjectList, false, ActorsToIgnore, EDrawDebugTrace::None, HitResult, true);
	if (bIsHit)
		return HitResult.ImpactPoint;
	return this->GetActorLocation();
}

void AAwCharacter::OnRep_Equipments()
{
	TArray<FString> EquipmentIdList;
	for (FEquipment ThisEquipment : CharacterObj.Equipments)
	{
		EquipmentIdList.Add(ThisEquipment.Id);
		//HideEquipmentAppearance(&ThisEquipment);
	}
	//Equipments.Empty();
	for (const FString EquipmentId : EquipmentIdList)
		this->WearEquipmentById(EquipmentId);
	RefreshEquipmentAppearance();
}


EChaElemental AAwCharacter::GetElemental(EChaElemental BaseElement, EElementalPickMethod PickMethod,
                                         ESlotInWeaponObj WeaponSlot) const
{
	switch (PickMethod)
	{
	case EElementalPickMethod::Set: return BaseElement;
	case EElementalPickMethod::Element:
		if (this->CharacterObj.WeaponSet.IsNull() == false)
		{
			return this->CharacterObj.WeaponSet.WeaponInSlot(WeaponSlot).Model.Elemental;
		}
		break;
	case EElementalPickMethod::Physical:
		if (this->CharacterObj.WeaponSet.IsNull() == false)
		{
			return this->CharacterObj.WeaponSet.WeaponInSlot(WeaponSlot).Model.Elemental;
		}
		break;
	}
	return BaseElement;
}

void AAwCharacter::AlignPlayerSpawnPointsNum(int points)
{
	if (localSpawnPointActors.Num() < points)
	{
		for (int i = localSpawnPointActors.Num(); i < points; i++)
		{
			FActorSpawnParameters SpawnParams;
			SpawnParams.Owner = this;
			SpawnParams.Instigator = this;
			AActor* SpawnPoint = GetWorld()->SpawnActor<AActor>(AActor::StaticClass(), GetActorLocation(),
			                                                    GetActorRotation(), SpawnParams);

			if (SpawnPoint)
			{
				USceneComponent* SceneRoot = NewObject<USceneComponent>(SpawnPoint, USceneComponent::StaticClass(),
				                                                        TEXT("SceneRoot"));
				SpawnPoint->SetRootComponent(SceneRoot);
				SpawnPoint->AttachToActor(this, FAttachmentTransformRules::KeepRelativeTransform);
				localSpawnPointActors.Add(SpawnPoint);
			}
		}
	}
}

void AAwCharacter::AddDefenseInfo(FDefenseInfo DefenseInfo)
{
	if (this->DefendingInfo.Contains(DefenseInfo)) return;
	this->DefendingInfo.Add(DefenseInfo);
	for (const FString BoxId : DefenseInfo.CharacterHitBoxId)
	{
		//UCharacterHitBox* HitBox = this->GetCharacterHitBoxByName(BoxId);	//TODO 即将干掉
		TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo = this->GetCharacterHitBoxByName(BoxId);
		UCharacterHitBoxData* HitBox = HitBoxInfo.Get<1>();
		if (HitBox)
		{
			HitBox->Active = true;
			HitBoxInfo.Get<0>()->SetActive(HitBox->Active);
		}
	}
}

void AAwCharacter::RemoveDefenseInfo(FDefenseInfo DefenseInfo)
{
	this->DefendingInfo.Remove(DefenseInfo);
	for (const FString BoxId : DefenseInfo.CharacterHitBoxId)
	{
		//UCharacterHitBox* HitBox = this->GetCharacterHitBoxByName(BoxId);	//TODO 即将干掉
		TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo = this->GetCharacterHitBoxByName(BoxId);
		UCharacterHitBoxData* HitBox = HitBoxInfo.Get<1>();
		if (HitBox)
		{
			HitBox->Active = HitBox->DefaultActive;
			HitBoxInfo.Get<0>()->SetActive(HitBox->Active);
		}
	}
}

void AAwCharacter::RemoveAllDefenseInfo()
{
	this->DefendingInfo.Empty();
}

bool AAwCharacter::IsHurtAction(FActionInfo ActionInfo) const
{
	if (!ActionComponent) return false;
	return ActionComponent->IsHurtAction(ActionInfo);
}

bool AAwCharacter::ModifyActionInfo(FString ActionOriginId, FActionInfoModifyApplication Modifer) const
{
	return GetActionComponent()->ModifyActionInfo(ActionOriginId, Modifer);
}

void AAwCharacter::ChangeMainAction(FActionSelection ActionSelection, FString SelectActionId) const
{
	GetActionComponent()->ChangeMainAction(ActionSelection, SelectActionId);
}

void AAwCharacter::SetPickupControl(bool On) const
{
	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter() == this)
		GetCmdComponent()->InCanInteraction = On;
}

bool AAwCharacter::Dead(bool IncludeSecondWind) const
{
	return (this->CharacterObj.CurrentRes.HP <= 0 && SecondWind <= 0) || (IncludeSecondWind == true && InSecondWind());
}

bool AAwCharacter::InSecondWind() const
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (UGameplayFuncLib::IsRogueMode() && SubSystem && IsPlayerCharacter())
	{
		int RevivedChance = SubSystem->GetRevivedChance_CurBattle();
		int RevivedChanceHasUse = SubSystem->GetRevivedChanceHasUse_CurBattle();
		bool CanRevived = RevivedChance - RevivedChanceHasUse >= 1;

		return this->CharacterObj.CurrentRes.HP <= 0 && (this->SecondWind > 0 || CanRevived);
	}
	return this->CharacterObj.CurrentRes.HP <= 0 && this->SecondWind > 0;
}


float AAwCharacter::HealthPercentage(bool RecheckProp)
{
	if (RecheckProp == true) this->AttrRecheck();
	return this->CharacterObj.CurrentRes.HP * 1.000f / (this->CharacterObj.CurProperty.HP * 1.000f);
}

float AAwCharacter::ManaPercentage() const
{
	return this->CharacterObj.CurrentRes.MP * 1.000f / (this->CharacterObj.CurProperty.MP * 1.000f);
}

float AAwCharacter::StrengthPercentage() const
{
	return this->CharacterObj.CurrentRes.SP * 1.000f / (this->CharacterObj.CurProperty.SP * 1.000f);
}

float AAwCharacter::AwakePercentage() const
{
	return this->CharacterObj.CurrentRes.AP * 1.000f / (this->CharacterObj.CurProperty.AP * 1.000f);
}

void AAwCharacter::Tick_RecoveryChaProp(float DeltaTime)
{
	// --- MP ---
	if (MP_Timer < MP_FreezeDur)
		MP_Timer += DeltaTime;
	else
	{
		if (CharacterObj.CurrentRes.MP < CharacterObj.CurProperty.MP)
		{
			const float RecoverySpeed = UGameplayFuncLib::GetDataManager()->DebugConfig.MP_RecoverySpeed;
			const float NewMP = CharacterObj.CurrentRes.MP * 1.000f + RecoverySpeed * DeltaTime;
			CharacterObj.CurrentRes.MP = FMath::Clamp(FMath::RoundToInt(NewMP), 0, CharacterObj.CurProperty.MP);
		}
	}

	// --- SP ---
	if (SP_Timer < SP_FreezeDur)
		SP_Timer += DeltaTime;
	else
	{
		if (CharacterObj.CurrentRes.SP < CharacterObj.CurProperty.SP)
		{
			const float RecoverySpeed = UGameplayFuncLib::GetDataManager()->DebugConfig.SP_RecoverySpeed;
			const float NewSP = CharacterObj.CurrentRes.SP * 1.000f + RecoverySpeed * DeltaTime;
			CharacterObj.CurrentRes.SP = FMath::Clamp(FMath::RoundToInt(NewSP), 0, CharacterObj.CurProperty.SP);
		}
	}

	if (GetCmdComponent()->IsSprint && CharacterObj.CurrentRes.SP > 0)
	{
		const float SprintConsumeSpeed = UGameplayFuncLib::GetDataManager()->DebugConfig.SP_SprintConsumeSpeed;
		const float NewSP = CharacterObj.CurrentRes.SP * 1.000f - SprintConsumeSpeed * DeltaTime;
		CharacterObj.CurrentRes.SP = FMath::Clamp(FMath::RoundToInt(NewSP), 0, CharacterObj.CurProperty.SP);
		SP_Timer = 0;
		SP_FreezeDur = UGameplayFuncLib::GetDataManager()->DebugConfig.SP_FreezeDur_Short;
		if (CharacterObj.CurrentRes.SP <= 0)
			GetCmdComponent()->IsSprint = false;
	}

	AttrRecheck();
	// const FString Str = FString::FromInt(CharacterObj.CurrentRes.SP)
	//                     .Append(" / ")
	//                     .Append(FString::FromInt(CharacterObj.CurProperty.SP));
	// UKismetSystemLibrary::PrintString(this, *Str);
}

int AAwCharacter::CurrentHP() const
{
	return CharacterObj.CurrentRes.HP;
}

void AAwCharacter::SetCurrentHP(int NewHealth)
{
	CharacterObj.CurrentRes.HP = NewHealth;
	CharacterObj.CurProperty.HP = NewHealth;
	// 修改最大值会被AttrCheck覆盖，没意义
	//CharacterObj.CurProperty.HP = MaxHealth;
	//CharacterObj.BaseProp.HP = MaxHealth;
}

int AAwCharacter::GetCurAirDodgePoint() const
{
	return CharacterObj.CurrentRes.AirDodgePoint;
}

void AAwCharacter::SetCurAirDodgePointToMax()
{
	CharacterObj.CurrentRes.AirDodgePoint = CharacterObj.CurProperty.AirDodgePoint;
}

bool AAwCharacter::CheckActionCost(const FActionCost ActionCost) const
{
	if (ActionCost.HP > 0 && CharacterObj.CurrentRes.HP < ActionCost.HP)
		return false;
	if (ActionCost.MP > 0 && CharacterObj.CurrentRes.MP < ActionCost.MP)
	{
		if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
		{
			UGameMain* GameMain = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);
			if (GameMain)
				GameMain->PlayPlayerStateBarManaNUllFX();
		}
		return false;
	}
	if (ActionCost.SP > 0 && CharacterObj.CurrentRes.SP <= 0)
		return false;
	if (CharacterObj.CurrentRes.AP < ActionCost.AP)
		return false;
	if (CharacterObj.CurrentRes.AirDodgePoint < ActionCost.AirDodgePoint)
		return false;
	return true;
}

void AAwCharacter::DeductActionCost(const FActionCost ActionCost)
{
	if (ActionCost.HP > 0)
		CharacterObj.CurrentRes.HP = FMath::Max(0, CharacterObj.CurrentRes.HP - ActionCost.HP);

	if (ActionCost.MP > 0)
	{
		CharacterObj.CurrentRes.MP = FMath::Max(0, CharacterObj.CurrentRes.MP - ActionCost.MP);
		MP_Timer = 0;
	}
	if (ActionCost.AP > 0)
	{
		CharacterObj.CurrentRes.AP = FMath::Max(0, CharacterObj.CurrentRes.AP - ActionCost.AP);
	}
	if (ActionCost.SP > 0)
	{
		CharacterObj.CurrentRes.SP = FMath::Max(0, CharacterObj.CurrentRes.SP - ActionCost.SP);
		SP_Timer = 0;
		if (CharacterObj.CurrentRes.SP == 0)
			SP_FreezeDur = UGameplayFuncLib::GetDataManager()->DebugConfig.SP_FreezeDur_Long;
		else
			SP_FreezeDur = UGameplayFuncLib::GetDataManager()->DebugConfig.SP_FreezeDur_Short;
	}
	if (ActionCost.AirDodgePoint > 0)
	{
		CharacterObj.CurrentRes.AirDodgePoint =
			FMath::Max(0, CharacterObj.CurrentRes.AirDodgePoint - ActionCost.AirDodgePoint);
	}
}

int AAwCharacter::HealthOfPercentage(float Percentage, bool RecheckProp)
{
	if (RecheckProp == true) AttrRecheck();
	return FMath::RoundToInt(Percentage * 100.000f * CharacterObj.CurProperty.HP);
}

bool AAwCharacter::HealthFull(bool RecheckProp)
{
	if (RecheckProp == true) this->AttrRecheck();
	return this->CharacterObj.CurrentRes.HP >= this->CharacterObj.CurProperty.HP;
}

float AAwCharacter::ZChange() const
{
	return GetActorLocation().Z - LastLocation.Z;
}

bool AAwCharacter::InFreezing() const
{
	if (!this->AwAnimInstance) return false;
	return AwAnimInstance->InFreezing();
}

FVector2D AAwCharacter::XYChange() const
{
	return FVector2D(GetActorLocation().X - LastLocation.X, GetActorLocation().Y - LastLocation.Y);
}

void AAwCharacter::AddElementalTriggerTags(TArray<FString> ToAddTags)
{
	for (const FString TheTag : ToAddTags)
	{
		if (this->ElementalTriggerTag.Contains(TheTag) == false)
			ElementalTriggerTag.Add(TheTag);
	}
}

void AAwCharacter::RemoveElementalTriggerTags(TArray<FString> ToAddTags)
{
	for (const FString TheTag : ToAddTags)
	{
		this->ElementalTriggerTag.Remove(TheTag);
	}
}

void AAwCharacter::CleanElementalTriggerTag()
{
	this->ElementalTriggerTag.Empty();
}

bool AAwCharacter::HasAnyElementalTriggerTag(TArray<FString> CheckTags) const
{
	for (const FString CheckTag : CheckTags)
	{
		if (this->ElementalTriggerTag.Contains(CheckTag)) return true;
	}
	return false;
}

bool AAwCharacter::HasElementalTriggerTag(FString CheckTag) const
{
	return this->ElementalTriggerTag.Contains(CheckTag);
}

bool AAwCharacter::HasAllTheElementalTriggerTags(TArray<FString> CheckTags) const
{
	for (const FString CheckTag : CheckTags)
	{
		if (ElementalTriggerTag.Contains(CheckTag) == false) return false;
	}
	return true;
}

void AAwCharacter::AddDragMove(FDragMoveInfo Drag) const
{
	GetMoveComponent()->AddDragForce(Drag);
}

void AAwCharacter::AddSqueeze(FVector SqueezeForce) const
{
	GetMoveComponent()->AddSqueeze(SqueezeForce);
}

void AAwCharacter::SetSqueezeType(ESqueezeType Type) const
{
	if (GetSqueezeComp())
		GetSqueezeComp()->SetSqueezeType(Type);
}

bool AAwCharacter::ChangeBattleClass(FString ToBattleClassId)
{
	//if (OwnerPlayerController != UGameplayFuncLib::GetMyAwPlayerController()) return false;	//不是我自己的
	if (this->PlayerClassId.IsEmpty()) return false;
	const FBattleClassModel CurClass = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(
		this->PlayerClassId);
	if (this->PlayerClassId != CurClass.Id) return false;
	if (CurClass.CanChangeClassId.Contains(ToBattleClassId) == false) return false;
	const FBattleClassModel TargetClass = UGameplayFuncLib::GetAwDataManager()->
		GetBattleClassModelById(ToBattleClassId);
	if (TargetClass.Id != ToBattleClassId) return false;

	//到这里应该就是可以换职业了，所以……
	const int WasBIndex = UAwGameInstance::Instance->RoleInfo.GetBattleClassIndexById(PlayerClassId);

	this->PlayerClassId = ToBattleClassId;
	const int BIndex = UAwGameInstance::Instance->RoleInfo.GetBattleClassIndexById(ToBattleClassId);
	int MainWeaponIndex = -1; //原来装备的武器在背包哪儿
	int OffWeaponIndex = -1;
	if (BIndex < 0)
	{
		//如果没有对应职业的记录，就创建记录
		UAwGameInstance::Instance->RoleInfo.BattleClassInfo.Add(FBattleClassObj(TargetClass));
	}
	else
	{
		//如果有，就获取对应的记录执行操作
		MainWeaponIndex = UAwGameInstance::Instance->RoleInfo.GetWeaponObjIndexByUniqueId(
			UAwGameInstance::Instance->RoleInfo.BattleClassInfo[BIndex].EquipmentSet.MainWeaponUniqueId
		);
		OffWeaponIndex = UAwGameInstance::Instance->RoleInfo.GetWeaponObjIndexByUniqueId(
			UAwGameInstance::Instance->RoleInfo.BattleClassInfo[BIndex].EquipmentSet.OffWeaponUniqueId
		);
	}

	FWeaponObj MainWeapon = FWeaponObj();
	FWeaponObj OffWeapon = FWeaponObj();
	if (MainWeaponIndex >= 0)
	{
		//原本的武器存在
		MainWeapon = UAwGameInstance::Instance->RoleInfo.WeaponObjs[MainWeaponIndex];
	}
	else if (TargetClass.DefaultWeapons.Num() > 0)
	{
		//原本的武器不存在，但是职业配置了默认武器
		FWeaponModel WeaponModel = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(
			TargetClass.DefaultWeapons[0]);
		if (WeaponModel.IsNull() == false)
		{
			MainWeapon = FWeaponObj(WeaponModel, true);
		}
	}
	if (OffWeaponIndex >= 0)
	{
		OffWeapon = UAwGameInstance::Instance->RoleInfo.WeaponObjs[OffWeaponIndex];
	}
	else if (TargetClass.DefaultWeapons.Num() > 1)
	{
		//原本的武器不存在，但是职业配置了默认武器
		FWeaponModel WeaponModel = UGameplayFuncLib::GetAwDataManager()->GetWeaponModelById(
			TargetClass.DefaultWeapons[1]);
		if (WeaponModel.IsNull() == false)
		{
			OffWeapon = FWeaponObj(WeaponModel, true);
		}
	}
	const TMap<ESlotInWeaponObj, FString> WasWeapon = this->WearWeapon(
		FEquippedWeaponSet(CharacterObj.TypeId, MainWeapon, OffWeapon));

	if (WasBIndex >= 0)
	{
		for (TTuple<ESlotInWeaponObj, FString> WeaponInfo : WasWeapon)
		{
			switch (WeaponInfo.Key)
			{
			case ESlotInWeaponObj::MainWeapon: UAwGameInstance::Instance->RoleInfo.BattleClassInfo[WasBIndex].
			                                   EquipmentSet.MainWeaponUniqueId = WeaponInfo.Value;
				break;
			case ESlotInWeaponObj::OffWeapon: UAwGameInstance::Instance->RoleInfo.BattleClassInfo[WasBIndex].
			                                  EquipmentSet.OffWeaponUniqueId = WeaponInfo.Value;
				break;
			default: break;
			}
		}
	}

	GetActionComponent()->AddActionsByClassModel(TargetClass, CharacterObj.TypeId);
	GetActionComponent()->ChangeStateActionsByClassModel(TargetClass);

	this->CharacterObj.ClassId = TargetClass.Id;

	PreorderAction(TargetClass.ActionOnChangeTo);

	return true;
}

TArray<FString> AAwCharacter::AchievementSignalBeep(FString SignalKey)
{
	if (!UGameplayFuncLib::GetAllLocalAwPlayerControllers().Contains(OwnerPlayerController)) return TArray<FString>();
	//不是玩家的就不能增加进度（TODO：如果小弟也可以增加进度那就再说吧）
	UKismetSystemLibrary::PrintString(this, FString("Beeping : ").Append(SignalKey));
	TArray<FString> DoneAchievements = UAwGameInstance::Instance->RoleInfo.BeepAchievement(SignalKey);
	for (FString DAch : DoneAchievements)
	{
		UKismetSystemLibrary::PrintString(this, FString("Done Achievement :: ").Append(DAch));
	}
	return DoneAchievements;
}

void AAwCharacter::GetReady()
{
	this->GotReady = true;
	GetAttackHitComponent()->Start();
}


void AAwCharacter::SetFixRotation(float TargetYaw, float FixUnderDegree) const
{
	if (MoveComponent) MoveComponent->SetFixRotation(TargetYaw, FixUnderDegree);
}

void AAwCharacter::StopFixRotation()
{
	if (MoveComponent) MoveComponent->StopFixRotation();
}

FTransform AAwCharacter::GetTransformByBindPointId(FString BindPointId)
{
	if (BindPointId.IsEmpty() == false && this->EquipmentBindPoints.Contains(BindPointId))
	{
		return EquipmentBindPoints[BindPointId]->GetComponentTransform();
	}
	return this->GetActorTransform();
}

void AAwCharacter::Pause()
{
	this->Paused = true;
	if (ActionComponent) ActionComponent->Paused = true;
	if (AwAnimInstance) AwAnimInstance->Paused = true;
	if (MoveComponent) MoveComponent->Paused = true;
	if (AIComponent) AIComponent->Paused = true;
	if (GetMesh()) GetMesh()->GlobalAnimRateScale = 0;
}

void AAwCharacter::Resume()
{
	this->Paused = false;
	if (ActionComponent) ActionComponent->Paused = false;
	if (AwAnimInstance) AwAnimInstance->Paused = false;
	if (MoveComponent) MoveComponent->Paused = false;
	if (AIComponent) AIComponent->Paused = false;
	if (GetMesh()) GetMesh()->GlobalAnimRateScale = 1;
}

float AAwCharacter::CurrentGlowExtends()
{
	if (GlowExtents.Num() <= 0) return 0;

	FGlowExtendTweenInfo BestOne = GlowExtents[0];
	for (int i = 1; i < GlowExtents.Num(); i++)
	{
		if (GlowExtents[i].BetterThan(BestOne))
		{
			BestOne = GlowExtents[i];
		}
	}

	return BestOne.ThisTickValue();
}

FLinearColor AAwCharacter::CurrentGlowColor()
{
	if (GlowExtents.Num() <= 0) return FLinearColor(23, 7, 0);

	return GlowExtents[GlowExtents.Num() - 1].Color;
}

void AAwCharacter::AddGlowExtendInfo(FGlowExtendTweenInfo Info)
{
	this->GlowExtents.Add(Info);
}

void AAwCharacter::SetGlowExtent(float GlowExtent, FLinearColor GlowColor)
{
	if (GlowExtent == LastGlowExtent && GlowColor == LastGlowColor)
		return;

	//处理变亮

	const FVector Color = FVector(GlowColor.R, GlowColor.G, GlowColor.B);

	for (UMeshComponent* MeshComp : GetAllMeshComponents())
	{
		if (MeshComp)
		{
			MeshComp->SetScalarParameterValueOnMaterials("GlowExtent", GlowExtent);
			MeshComp->SetVectorParameterValueOnMaterials("Color", Color);
		}
	}

	LastGlowExtent = GlowExtent;
	LastGlowColor = GlowColor;
}

TArray<UActorComponent*> AAwCharacter::GetAllViewActorComponents()
{
	TArray<UActorComponent*> ResComponents;
	for (const TTuple<FString, UMeshComponent*> BodyPartHere : BodySightParts)
		ResComponents.Add(Cast<UActorComponent>(BodyPartHere.Value));

	for (const FWearingAppearanceInfo AppInfo : this->WearingAppearance)
	{
		if (AppInfo.Actor)
		{
			TArray<UActorComponent*> AppComps;
			AppInfo.Actor->GetComponents(AppComps, true);
			ResComponents.Append(AppComps);
		}
	}

	return ResComponents;
}

void AAwCharacter::HideInCamera()
{
	TArray<UActorComponent*> ToHide = GetAllViewActorComponents();
	for (UActorComponent* Hide : ToHide)
	{
		USkeletalMeshComponent* SkeletalMesh = Cast<USkeletalMeshComponent>(Hide);
		if (SkeletalMesh)
		{
			SkeletalMesh->SetVisibility(false);
			continue;
		}

		UStaticMeshComponent* StaticMesh = Cast<UStaticMeshComponent>(Hide);
		if (StaticMesh)
		{
			StaticMesh->SetVisibility(false);
			continue;
		}
	}
	for (const TTuple<FString, UChildActorComponent*> BodyPartHere : BodySightActorParts)
	{
		if (BodyPartHere.Value)
		{
			BodyPartHere.Value->SetVisibility(false);
			continue;
		}
	}
	GetMesh()->SetVisibility(false);
	SetAppearanceVisible(false);
}

void AAwCharacter::RestoreInCamera()
{
	TArray<UActorComponent*> ToHide = GetAllViewActorComponents();
	for (UActorComponent* Hide : ToHide)
	{
		USkeletalMeshComponent* SkeletalMesh = Cast<USkeletalMeshComponent>(Hide);
		if (SkeletalMesh)
		{
			SkeletalMesh->SetVisibility(true);
			continue;
		}

		UStaticMeshComponent* StaticMesh = Cast<UStaticMeshComponent>(Hide);
		if (StaticMesh)
		{
			StaticMesh->SetVisibility(true);
			continue;
		}
	}
	for (const TTuple<FString, UChildActorComponent*> BodyPartHere : BodySightActorParts)
	{
		if (BodyPartHere.Value)
		{
			BodyPartHere.Value->SetVisibility(true);
			continue;
		}
	}
	GetMesh()->SetVisibility(true);
	SetAppearanceVisible(true);
}

void AAwCharacter::OnDead()
{
	//执行所有Buff的OnRemoved并且移除
	for (FBuffObj& Buff : this->CharacterObj.Buff)
	{
		UBuffManager::RemoveBuff(Buff, false);
	}
	CharacterObj.Buff.Empty();
	//不同职业做不同的事情
	if (this->IsPlayerCharacter())
	{
		//玩家角色死亡
		if (UGameplayFuncLib::GetBGMManager())
			UGameplayFuncLib::PlayBgmByKey("GameOver");
		if (UAwGameInstance::Instance->isSurvivor)
			UGameplayFuncLib::GetAwGameInstance()->UIManager->Show("RogueDead_Svl");
		else
			UGameplayFuncLib::GetAwGameInstance()->UIManager->Show("RogueDead");
		//执行OnBeKill的蓝图回调
		this->OnBeKilled();
		this->OnCharacterDeadDelegate.Broadcast(this);
	}
	else
	{
		if (MobClassId.IsEmpty() == false)
		{
			//怪物死亡
			GetAIComponent()->SetTargetEnemy(FAIFindChaInfo());
			UGameplayFuncLib::GetLootManager()->DropLootPackageFromMob(MobClassId,
			                                                           GetActorLocation() + FVector(
				                                                           0, 0, -this->GetCapsuleComponent()->
				                                                           GetScaledCapsuleHalfHeight() + 1));
			//CharacterDeadTrigger
			UGameplayFuncLib::GetAwGameInstance()->TriggerManager->AddCharacterDead(this);
			if (this->GetSqueezeComp())
				this->GetSqueezeComp()->CloseSqueeze();
			GetCapsuleComponent()->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn,
			                                                     ECollisionResponse::ECR_Overlap);

			const FMobModel MobModel = UGameplayFuncLib::GetAwDataManager()->GetMobModelById(MobClassId,CharacterObj.MobAlterId);
			int Exp = UGameplayFuncLib::GetAwDataManager()->GetMobExp(MobClassId,CharacterObj.MobAlterId);
			if (MobModel.Id.IsEmpty() == false)
			{
				//玩家获得经验
				GainExp(Exp);

				//先改变阵营声望
				for (const FMobDungeonCampImpact DCImpact : MobModel.DungeonCampImpacts)
				{
					UGameplayFuncLib::GetAwGameInstance()->CampProgressModify(
						DCImpact.DungeonId, DCImpact.CampId, DCImpact.Value);
				}

				//执行BeKilled
				for (const FString OnBeKilled : MobModel.OnBeKilled)
				{
					const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(OnBeKilled);
					UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
					if (Func)
					{
						struct
						{
							AAwCharacter* DeadGuy;
							TArray<FString> Params;
						} FuncParam;
						FuncParam.DeadGuy = this;
						FuncParam.Params = FuncData.Params;
						this->ProcessEvent(Func, &FuncParam);
					}
				}
				//执行OnBeKill的蓝图回调
				this->OnBeKilled();
				this->OnCharacterDeadDelegate.Broadcast(this);
			}
		}
		else
		{
			//小弟死亡
		}
	}
}

bool AAwCharacter::ShouldRunAI() const
{
	return this->ActionComponent
		       ? (
			       ActionComponent->IsInMontageAction() == false
		       )
		       : true;
}

TArray<FString> AAwCharacter::CurrentUseActionTags() const
{
	if (!AIComponent) return TArray<FString>();
	return AIComponent->CurrentUseActionTags();
}

void AAwCharacter::SetUseActionTags(TArray<FString> UseActionTags) const
{
	if (!AIComponent) return;
	return AIComponent->SetUseActionTags(UseActionTags);
}

FVector AAwCharacter::GetAITargetDirection() const
{
	if (!AIComponent) return this->GetActorRotation().Vector();
	return AIComponent->GetTargetDirection();
}

void AAwCharacter::SetToArms(EArmState ToArms) const
{
	if (!ActionComponent) return;
	ActionComponent->SetToArms(ToArms);
}

EArmState AAwCharacter::GetArmState() const
{
	if (!ActionComponent) return EArmState::Unarmed;
	return ActionComponent->GetArmState();
}

FRotator AAwCharacter::SetAimDirection()
{
	if (IsPlayerCharacter() == false || !CmdComponent || UGameplayFuncLib::GetAllLocalAwPlayerControllers().Num() == 0)
		return GetActorRotation();
	if (!CameraComponent)
		return GetActorRotation();
	AimDirection = FRotator(
		CameraComponent->GetCameraRotate().Pitch,
		GetActorRotation().Yaw,
		GetActorRotation().Roll
	);

	// UKismetSystemLibrary::PrintString(this, FString("Setting Aim:").Append(AimDirection.ToString()),
	// 	true, true, FLinearColor::Yellow, 30);

	return AimDirection;
}

void AAwCharacter::SetSpeedInputAcceptanceModifier(FInputAcceptanceModifier Modifer)
{
	this->SpeedInputAcceptanceModifier = Modifer;
}

bool AAwCharacter::IsSameClan(const AAwCharacter* Target) const
{
	return (this->Side == Target->Side);
}

void AAwCharacter::SetCurrentActionToActionCheckList(bool Active) const
{
	if (ActionComponent)
		ActionComponent->AddCurrentActionToCheckList = Active;
}

void AAwCharacter::StopAttachedVFX(FString Id)
{
	int i = 0;
	while (i < PlayingFX.Num())
	{
		if (PlayingFX[i].Id == Id)
		{
			for (const TTuple<USceneComponent*, UParticleSystemComponent*> Vfx : PlayingFX[i].VFX)
			{
				if (IsValid(Vfx.Value))
				{
					Vfx.Value->DestroyComponent();
				}
			}
			PlayingFX.RemoveAt(i);
		}
		else
		{
			i++;
		}
	}
}

bool AAwCharacter::PlayAttachedVFX(FString EffectPath, FString Id, EVFXBindPointType BindOn, bool PlayOnce)
{
	//是否已经存在了
	FPlayingFXOnCharacter ToCreate;
	ToCreate.Id = Id;
	if (this->PlayingFX.FindByPredicate([ToCreate](FPlayingFXOnCharacter PFX)
	{
		return PFX.Id == ToCreate.Id;
	}))
		return false;

	//能否创建粒子
	ToCreate.VFXPath = EffectPath;
	UParticleSystem* Template = LoadObject<UParticleSystem>(nullptr, *UResourceFuncLib::GetAssetPath(EffectPath));
	if (!Template) return false;

	if (BindOn == EVFXBindPointType::Mesh && IsValid(GetMesh()))
	{
		ToCreate.VFX.Add(
			GetMesh(),
			UGameplayStatics::SpawnEmitterAttached(
				Template, GetMesh(), NAME_None, FVector::ZeroVector, FRotator::ZeroRotator,
				FVector::OneVector, EAttachLocation::KeepRelativeOffset,
				true, EPSCPoolMethod::None, true)
		);
	}
	else
	{
		for (UFXPlayPoint* BPoint : this->FXPoint)
		{
			if (BPoint->PointType == BindOn)
			{
				ToCreate.VFX.Add(
					BPoint,
					UGameplayStatics::SpawnEmitterAttached(
						Template, BPoint, NAME_None, FVector::ZeroVector, FRotator::ZeroRotator,
						FVector::OneVector, EAttachLocation::KeepRelativeOffset,
						true, EPSCPoolMethod::AutoRelease, true)
				);
			}
		}
	}

	ToCreate.Priority = PlayingFX.Num();
	ToCreate.PlayOnce = PlayOnce;
	PlayingFX.Add(ToCreate);

	return true;
}


void AAwCharacter::InteractWidgetVisible(bool SetTo) const
{
	if (!InteractWidget) return;
	InteractWidget->SetVisibility(SetTo);
}

void AAwCharacter::PlaySpawnFX_Implementation()
{
	this->GetReady();
}

void AAwCharacter::HideMeshAndCollision_Implementation(bool bHide)
{
	if (bHide)
	{
		HideInCamera();
		GetCapsuleComponent()->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn,
		                                                     ECollisionResponse::ECR_Ignore);
		SetAllCharacterHitBoxActive(false);
	}
	else
	{
		RestoreInCamera();
		GetCapsuleComponent()->
			SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
		SetAllCharacterHitBoxActive(true);
	}
}

void AAwCharacter::OnLockTargetSwitch_Implementation(bool bSwitchLeft)
{
	if (this->IsPlayerCharacter())
	{
		UAwCameraComponent* AwCameraComponent = Cast<UAwCameraComponent>(
			GetComponentByClass(UAwCameraComponent::StaticClass()));
		if (IsValid(AwCameraComponent))
		{
			AwCameraComponent->SwitchLockTarget(bSwitchLeft);
		}
	}
}

void AAwCharacter::SetSkin(USkeletalMeshComponent* InMesh, TMap<int, UMaterialInterface*> Materials)
{
	if (InMesh)
	{
		for (auto& Elem : Materials)
		{
			InMesh->SetMaterial(Elem.Key, Elem.Value);
		}
	}
}

void AAwCharacter::SetChildSkin(UChildActorComponent* ChildActor, UMaterialInterface* Material)
{
	USceneComponent* child = ChildActor->GetChildComponent(0)->GetChildComponent(0);
	USkeletalMeshComponent* mesh = Cast<USkeletalMeshComponent>(child);
	if (IsValid(mesh))
		mesh->SetMaterial(0, Material);
}

void AAwCharacter::ActiveECSAttackBoxs(TArray<FString> BoxNames)
{
	auto flag = UFlagmarkBitPool::Acquire();
	for (const FString ThisHitBoxName : BoxNames)
		GetAttackHitComponent()->ActiveECSAttack(ThisHitBoxName, flag);
}

void AAwCharacter::DeactiveECSAttackBoxs(TArray<FString> BoxNames)
{
	for (const FString ThisHitBoxName : BoxNames)
		GetAttackHitComponent()->DeactiveECSAttack(ThisHitBoxName);
}
